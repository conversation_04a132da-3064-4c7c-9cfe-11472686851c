/**
 * 策略生成AI服务
 */

import { BaseAiService, AiServiceError, AI_ERROR_CODES } from './baseAiService';
import { AiServiceType, StrategyGenerationRequest, StrategyGenerationResponse } from '../types';

export class StrategyGenerationService extends BaseAiService<StrategyGenerationRequest, StrategyGenerationResponse> {
  constructor() {
    super(AiServiceType.STRATEGY_GENERATION);
  }

  /**
   * 验证策略生成请求
   */
  protected validateRequest(requestBody: StrategyGenerationRequest): string | null {
    if (!requestBody.userInput || typeof requestBody.userInput !== 'string') {
      return 'userInput is required and must be a string';
    }

    if (requestBody.userInput.trim().length === 0) {
      return 'userInput cannot be empty';
    }

    if (requestBody.userInput.length > 10000) {
      return 'userInput is too long (max 10000 characters)';
    }

    // 验证可选参数
    if (requestBody.riskLevel && !['low', 'medium', 'high'].includes(requestBody.riskLevel)) {
      return 'riskLevel must be one of: low, medium, high';
    }

    return null;
  }

  /**
   * 处理AI响应
   */
  protected async processAiResponse(aiResponse: any, promptVersion: string): Promise<StrategyGenerationResponse> {
    const aiContent = aiResponse.choices?.[0]?.message?.content;

    if (!aiContent) {
      throw new AiServiceError(
        'No response content from AI model',
        AI_ERROR_CODES.INVALID_RESPONSE_FORMAT
      );
    }

    try {
      // 尝试解析AI返回的JSON
      const parsedResponse = JSON.parse(aiContent.trim());

      // 检查是否有包装层 (AI可能返回 {trade_strategy: {...}} 或直接返回 {...})
      let tradeStrategy;
      if (parsedResponse.trade_strategy) {
        // 有包装层，提取内部的trade_strategy
        tradeStrategy = parsedResponse.trade_strategy;
      } else if (parsedResponse.indicators || parsedResponse.signals || parsedResponse.outputs) {
        // 直接是trade_strategy结构
        tradeStrategy = parsedResponse;
      } else {
        throw new AiServiceError(
          'AI response does not contain valid trade strategy structure',
          AI_ERROR_CODES.INVALID_RESPONSE_FORMAT
        );
      }

      // 基本验证trade_strategy结构
      const validationError = this.validateTradeStrategyStructure(tradeStrategy);
      if (validationError) {
        throw new AiServiceError(
          `Invalid trade strategy structure: ${validationError}`,
          AI_ERROR_CODES.VALIDATION_FAILED
        );
      }

      return {
        success: true,
        tradeStrategy,
        metadata: {
          promptVersion,
          model: 'gpt-4o',
          tokensUsed: aiResponse.usage?.total_tokens
        }
      };
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', parseError);
      throw new AiServiceError(
        `AI response is not valid JSON format: ${parseError instanceof Error ? parseError.message : 'Unknown parse error'}`,
        AI_ERROR_CODES.INVALID_RESPONSE_FORMAT
      );
    }
  }

  /**
   * 验证trade_strategy结构
   */
  private validateTradeStrategyStructure(tradeStrategy: any): string | null {
    if (!tradeStrategy || typeof tradeStrategy !== 'object') {
      return 'Trade strategy must be an object';
    }

    if (!Array.isArray(tradeStrategy.signals)) {
      return 'signals must be an array';
    }

    if (!tradeStrategy.outputs || typeof tradeStrategy.outputs !== 'object') {
      return 'outputs must be an object';
    }

    // 验证indicators结构（可选）
    if (tradeStrategy.indicators && !Array.isArray(tradeStrategy.indicators)) {
      return 'indicators must be an array if provided';
    }

    if (tradeStrategy.indicators) {
      for (let i = 0; i < tradeStrategy.indicators.length; i++) {
        const indicator = tradeStrategy.indicators[i];
        if (!indicator.id || !indicator.type) {
          return `indicators[${i}] must have id and type`;
        }
      }
    }

    // 验证signals结构
    for (let i = 0; i < tradeStrategy.signals.length; i++) {
      const signal = tradeStrategy.signals[i];
      if (!signal.id || !signal.type) {
        return `signals[${i}] must have id and type`;
      }
    }

    // 验证outputs结构
    if (!tradeStrategy.outputs.buy_signal || !tradeStrategy.outputs.sell_signal) {
      return 'outputs must have buy_signal and sell_signal';
    }

    return null;
  }
}
