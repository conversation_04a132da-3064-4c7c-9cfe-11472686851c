# API Testing Guide

This guide explains how to test the My Invest Strategy Portfolio API using the local Swagger UI interface.

## Quick Start

1. **Install dependencies** (first time only):
   ```bash
   npm install
   ```

2. **Start Swagger UI server**:
   ```bash
   npm run swagger:serve
   ```

3. **Open browser** and navigate to <http://localhost:8888>

4. **Authenticate** using one of two methods:

   **Method 1: Cookie Authentication (Recommended)**
   - Login to `https://api.myinvestpilot.com` in another browser tab
   - Return to Swagger UI - authentication is automatic!
   - No manual configuration needed

   **Method 2: Header Authentication (Fallback)**
   - Click "Authorize" button in Swagger UI
   - Enter your email address in the "headerAuth" section
   - Click "Authorize"

5. **Test API endpoints** using the interactive interface

## Strategy Types Testing

The API supports two types of trading strategies. Make sure to test both:

### Code Strategy Testing

Test traditional code-based strategies that reference pre-defined strategy implementations.

**Example Request** (Create Portfolio):
```json
{
  "name": "Test Code Strategy Portfolio",
  "description": "Testing traditional code strategy format",
  "symbols": ["159915", "159928"],
  "start_date": "2020-01-01",
  "currency": "CNY",
  "market": "China",
  "commission": 0.0001,
  "update_time": "01:00:00",
  "strategy": {
    "name": "DualMovingAverageStrategy",
    "params": {
      "short_window": 10,
      "long_window": 30
    }
  },
  "capital_strategy": {
    "name": "PercentCapitalStrategy",
    "params": {
      "percents": 20,
      "initial_capital": 100000
    }
  }
}
```

### DSL Strategy Testing

Test new DSL-based strategies with embedded trading logic.

**Example Request** (Create Portfolio):
```json
{
  "name": "Test DSL Strategy Portfolio",
  "description": "Testing new DSL strategy format",
  "symbols": ["159915", "159928"],
  "start_date": "2020-01-01",
  "currency": "CNY",
  "market": "China",
  "commission": 0.0001,
  "update_time": "01:00:00",
  "strategy_definition": {
    "trade_strategy": {
      "indicators": {
        "sma_short": {"type": "SMA", "period": 10},
        "sma_long": {"type": "SMA", "period": 30}
      },
      "signals": {
        "buy": "sma_short > sma_long",
        "sell": "sma_short < sma_long"
      },
      "outputs": {
        "position": "if(buy, 1, if(sell, 0, prev(position)))"
      }
    },
    "capital_strategy": {
      "name": "PercentCapitalStrategy",
      "params": {
        "percents": 20,
        "initial_capital": 100000
      }
    }
  }
}
```

## Testing Checklist

### Basic Portfolio Operations
- [ ] Create Code Strategy Portfolio
- [ ] Create DSL Strategy Portfolio  
- [ ] Get Portfolio Details (both types)
- [ ] Update Portfolio (same strategy type)
- [ ] Update Portfolio (convert between strategy types)
- [ ] Delete Portfolio

### Search and Public APIs
- [ ] Search Symbols
- [ ] Get Public Portfolios by Symbol
- [ ] Get Official Portfolios

### Data Validation
- [ ] Verify database storage format for Code Strategy
- [ ] Verify database storage format for DSL Strategy
- [ ] Check parameter cleanup during strategy type conversion
- [ ] Validate API response formats

### Error Handling
- [ ] Test with invalid authentication
- [ ] Test with malformed requests
- [ ] Test with non-existent portfolios
- [ ] Test data integrity error scenarios

## Environment Configuration

The OpenAPI specification (`doc/openapi.yaml`) defines multiple server environments:

- **Production**: `https://myinveststrategyportfolio.madawei.workers.dev/strategy_portfolio`
- **Local Development**: `http://localhost:8787/strategy_portfolio`

You can switch between environments by modifying the `servers` section in the OpenAPI spec or by using the server dropdown in Swagger UI.

## Troubleshooting

### Common Issues

1. **Server not responding**: Ensure your target API server is running
2. **Authentication errors**: Verify your email is correctly entered in the authorization
3. **CORS issues**: Make sure your API server allows requests from localhost:8888
4. **Invalid requests**: Check the request format against the schema definitions

### Debug Information

The Swagger UI server logs all API requests and responses to the console. Check the terminal output for debugging information.

### Health Check

Visit <http://localhost:8888/health> to verify the Swagger UI server is running correctly.

## Advanced Testing

### Strategy Type Conversion

Test converting portfolios between strategy types:

1. Create a Code Strategy portfolio
2. Update it with DSL Strategy format
3. Verify old `strategy_params` are cleaned up
4. Convert back to Code Strategy
5. Verify old `trade_strategy_dsl` is cleaned up

### Data Integrity

Test data integrity protection:

1. Manually create inconsistent data scenarios (if possible)
2. Verify the API returns appropriate error messages
3. Check that MQ message construction handles edge cases

This comprehensive testing approach ensures both strategy formats work correctly and the system maintains data integrity during transitions.
