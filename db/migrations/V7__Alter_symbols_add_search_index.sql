-- Create a B-tree index for exact matches
CREATE INDEX idx_symbols_symbol ON symbols(symbol);
CREATE INDEX idx_symbols_name ON symbols(name);

-- Create GIN indexes for full text search
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE INDEX idx_symbols_symbol_trgm ON symbols USING gin (symbol gin_trgm_ops);
CREATE INDEX idx_symbols_name_trgm ON symbols USING gin (name gin_trgm_ops);

-- Create a composite index for the is_deleted filter
CREATE INDEX idx_symbols_is_deleted ON symbols(is_deleted) WHERE is_deleted = false;