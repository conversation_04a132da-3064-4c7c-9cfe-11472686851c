-- 修复组合描述中的 $1 问题
UPDATE portfolios
SET description = 
    CASE 
        WHEN description LIKE '%基于定投分析的$1模拟组合%' AND code LIKE '%_us_4%' THEN '基于定投分析的美股行业ETF模拟组合'
        WHEN description LIKE '%基于趋势技术分析的$1模拟组合%' AND code LIKE '%_us_1%' THEN '基于趋势技术分析的美股杠杆ETF模拟组合'
        WHEN description LIKE '%基于趋势技术分析的$1模拟组合%' AND code LIKE '%_us_2%' THEN '基于趋势技术分析的美股精选ETF模拟组合'
        WHEN description LIKE '%基于趋势技术分析的$1模拟组合%' AND code LIKE '%_us_3%' THEN '基于趋势技术分析的美股纳指三倍做多ETF模拟组合'
        WHEN description LIKE '%基于双均线分析的$1模拟组合%' AND code LIKE '%_us_global%' THEN '基于双均线分析的美股全球ETF模拟组合'
        WHEN description LIKE '%基于双均线分析的$1模拟组合%' AND code LIKE '%_cn_1%' THEN '基于双均线分析的A股精选ETF模拟组合'
        WHEN description LIKE '%基于双均线分析的$1模拟组合%' AND code LIKE '%_cn_2%' THEN '基于双均线分析的A股创业板ETF模拟组合'
        WHEN description LIKE '%基于双均线分析的$1模拟组合%' AND code LIKE '%_cn_3%' THEN '基于双均线分析的A股ETF模拟组合'
        WHEN description LIKE '%基于双均线分析的$1模拟组合%' AND code LIKE '%_cn_global%' THEN '基于双均线分析的A股全球ETF模拟组合'
        WHEN description LIKE '%基于双均线分析的$1模拟组合%' AND code LIKE '%_cn_qqq%' THEN '基于双均线分析的A股纳指ETF模拟组合'
        WHEN description LIKE '%基于趋势技术分析的$1模拟组合%' AND code LIKE '%_cc_1%' THEN '基于趋势技术分析的加密币模拟组合'
        WHEN description LIKE '%基于趋势技术分析的$1模拟组合%' AND code LIKE '%_cc_2%' THEN '基于趋势技术分析的BTC模拟组合'
        ELSE description
    END
WHERE description LIKE '%$1模拟组合%';
