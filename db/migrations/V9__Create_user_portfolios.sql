-- 创建用户组合关联表
CREATE TABLE user_portfolios (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL,
    portfolio_id UUID NOT NULL,
    is_deleted BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX idx_user_portfolios_email ON user_portfolios(email);
CREATE INDEX idx_user_portfolios_portfolio_id ON user_portfolios(portfolio_id);

-- 添加外键约束
ALTER TABLE user_portfolios
ADD CONSTRAINT fk_user_portfolios_portfolio
FOREIGN KEY (portfolio_id)
REFERENCES portfolios(id)
ON DELETE CASCADE;

-- 创建唯一约束，确保每个用户对每个组合只有一个有效的关联
CREATE UNIQUE INDEX idx_user_portfolios_unique_active
ON user_portfolios(email, portfolio_id)
WHERE NOT is_deleted;

-- 创建触发器来自动更新 updated_at 列
CREATE OR REPLACE FUNCTION update_user_portfolios_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_user_portfolios_updated_at
BEFORE UPDATE ON user_portfolios
FOR EACH ROW
EXECUTE FUNCTION update_user_portfolios_updated_at();
