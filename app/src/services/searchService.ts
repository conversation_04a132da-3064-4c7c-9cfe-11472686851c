import { Context } from 'hono';
import { query } from './databaseService';
import { getDBContext } from '../utils/helpers';

export async function searchSymbols(c: Context) {
  const q = c.req.query('q');
  const country = c.req.query('c');
  const type = c.req.query('t');
  const currency = c.req.query('cur');

  if (!q) {
    return c.json({ error: 'Query parameter is required' }, 400);
  }

  try {
    let whereConditions = [
      '(symbol ILIKE $1 OR name ILIKE $1 OR symbol % $2 OR name % $2)',
      'is_deleted = false'
    ];
    let values = [`%${q}%`, q];
    let valueIndex = 3;

    if (country) {
      whereConditions.push(`country = $${valueIndex}`);
      values.push(country);
      valueIndex++;
    }

    if (type) {
      whereConditions.push(`type = $${valueIndex}`);
      values.push(type.toUpperCase());
      valueIndex++;
    }

    if (currency) {
      whereConditions.push(`currency = $${valueIndex}`);
      values.push(currency.toUpperCase());
      valueIndex++;
    }

    const result = await query(getDBContext(c), `
      SELECT symbol, name, type, market, country, currency
      FROM strategy.symbols
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY
        CASE
          WHEN symbol ILIKE $2 OR name ILIKE $2 THEN 0
          WHEN symbol ILIKE $1 OR name ILIKE $1 THEN 1
          ELSE 2
        END,
        GREATEST(similarity(symbol, $2), similarity(name, $2)) DESC
      LIMIT 10
    `, values);

    return c.json({ results: result.rows });
  } catch (error) {
    console.error('Database query error:', error);
    return c.json({ error: 'An error occurred while searching symbols' }, 500);
  }
}
