# AI策略MQ测试工具

这个工具集用于端到端测试AI生成的交易策略，通过发送到MQ触发下游回测服务来验证策略的实际可用性。

## 🏗️ 架构

```
test_ai.py          # AI策略生成测试，输出JSON文件
     ↓
test-results/       # 存储AI生成的策略结果
     ↓
send_to_mq.py      # 读取策略文件，发送到MQ
     ↓
Redis MQ           # invest-strategy-service-ingestion-queue
     ↓
回测服务            # 下游服务进行实际回测验证
```

## 🚀 快速开始

### 1. 安装依赖
```bash
cd tool
pip install -r requirements.txt
```

### 2. 配置环境变量
确保 `.env` 文件包含Redis配置：
```bash
UPSTASH_REDIS_REST_URL=https://rare-lizard-21313.upstash.io
UPSTASH_REDIS_REST_TOKEN=你的token
```

### 3. 运行AI测试
```bash
python test_ai.py
```
这会：
- 测试所有AI prompt版本 (v1.0, v1.1)
- 生成HTML报告
- **保存成功的策略到JSON文件** (新功能)

### 4. 发送策略到MQ
```bash
# 发送最新的测试结果
python send_to_mq.py --latest

# 或指定特定文件
python send_to_mq.py test-results/ai-test-2024-01-01_12-00-00.json
```

## 📊 测试用例

工具会测试以下6个用例，每个用例在v1.0和v1.1版本下都会测试：

1. **基础双均线策略（中文）** → `test_dual_ma_cn_v1.0/v1.1`
2. **Basic Dual MA Strategy (English)** → `test_dual_ma_en_v1.0/v1.1`
3. **RSI策略** → `test_rsi_strategy_v1.0/v1.1`
4. **复杂多指标策略** → `test_complex_multi_v1.0/v1.1`
5. **JSON补全** → `test_json_completion_v1.0/v1.1`
6. **混合内容** → `test_mixed_content_v1.0/v1.1`

## 🎯 组合配置

每个AI生成的策略会被包装成完整的组合配置：

```json
{
  "name": "AI测试策略-基础双均线策略（中文）-v1.0",
  "code": "test_dual_ma_cn_v1.0",
  "description": "AI生成的测试策略: 基础双均线策略（中文） (版本: v1.0)",
  "strategy_definition": {
    "trade_strategy": { /* AI生成的策略 */ },
    "capital_strategy": {
      "name": "PercentCapitalStrategy",
      "params": { "initial_capital": 100000, "percents": 95 }
    }
  },
  "symbols": [
    {"symbol": "159915", "name": "创业板ETF"},
    {"symbol": "513100", "name": "纳指ETF"},
    {"symbol": "518880", "name": "黄金ETF"}
  ],
  "start_date": "2023-01-01",
  "market": "China",
  "currency": "CNY",
  "commission": 0.0001,
  "update_time": "01:00"
}
```

## 🔍 验证方式

1. **自动验证**: 脚本会显示发送状态和组合ID
2. **人工验证**: 通过组合ID在下游系统中检查回测结果
3. **固定ID**: 使用固定的测试ID，重复测试会覆盖之前的结果

## 📁 文件结构

```
tool/
├── test_ai.py              # AI测试脚本 (已修改)
├── send_to_mq.py           # MQ发送脚本 (新增)
├── test-results/           # 测试结果目录 (自动创建)
│   ├── ai-test-2024-01-01_12-00-00.json
│   ├── ai-test-2024-01-01_12-00-00.html
│   └── ...
├── .env                    # 环境配置 (已更新)
├── requirements.txt        # 依赖列表 (已更新)
└── README_AI_MQ_TEST.md   # 本文档
```

## 🛠️ 故障排除

### Redis连接失败
- 检查 `.env` 文件中的Redis配置
- 确认token有效性

### 没有测试结果文件
- 先运行 `python test_ai.py` 生成结果
- 检查 `test-results/` 目录

### AI生成失败
- 检查API服务是否运行 (localhost:8787)
- 查看HTML报告了解具体错误

## 💡 使用技巧

1. **批量测试**: 可以多次运行 `send_to_mq.py --latest` 重复发送
2. **选择性发送**: 可以手动编辑JSON文件，只发送特定策略
3. **监控日志**: 脚本会显示详细的发送状态和组合ID
4. **固定ID**: 相同测试用例会覆盖之前的结果，避免垃圾数据

这个工具让我们能够真正验证AI生成策略的实用性，而不仅仅是格式正确性！
