-- 更新策略描述
UPDATE strategies 
SET description = CASE name
    WHEN 'ChandelierExitMAStrategy' THEN '趋势技术分析策略'
    WHEN 'BuyHoldStrategy' THEN '定投分析策略'
    WHEN 'DualMovingAverageStrategy' THEN '双均线分析策略'
    WHEN 'PercentCapitalStrategy' THEN '百分比资金分配策略'
    WHEN 'SimplePercentCapitalStrategy' THEN '简单百分比分配策略'
    WHEN 'AnnualFixedInvestmentStrategy' THEN '年度定额分配策略'
END
WHERE name IN (
    'ChandelierExitMAStrategy',
    'BuyHoldStrategy',
    'DualMovingAverageStrategy',
    'PercentCapitalStrategy',
    'SimplePercentCapitalStrategy',
    'AnnualFixedInvestmentStrategy'
);

-- 更新组合描述
UPDATE portfolios
SET description = CASE 
    WHEN description LIKE '%基于特定止损与均线策略%' THEN regexp_replace(description, '基于特定止损与均线策略的(.*?)组合', '基于趋势技术分析的$1模拟组合')
    WHEN description LIKE '%基于买入持有定投策略%' THEN regexp_replace(description, '基于买入持有定投策略的(.*?)组合', '基于定投分析的$1模拟组合')
    WHEN description LIKE '%基于双均线策略%' THEN regexp_replace(description, '基于双均线策略的(.*?)组合', '基于双均线分析的$1模拟组合')
END
WHERE description ~ '基于(特定止损与均线|买入持有定投|双均线)策略';