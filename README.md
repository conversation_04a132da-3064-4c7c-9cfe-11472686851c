# myInvestStrategyPortfolio

This project enables users to manage their portfolios based on investment strategies they choose. It supports both traditional code-based strategies and new DSL-based strategies, and can trigger portfolios into time fly mode to analyze performance changes.

## Project Structure

```text
myInvestStrategyPortfolio/
├── app/                    # Cloudflare Worker application
│   ├── src/               # Source code
│   └── package.json       # Worker dependencies
├── db/                    # Database migrations and configuration
│   └── migrations/        # Flyway migration files
├── doc/                   # Documentation
│   ├── api/              # API documentation
│   │   ├── openapi.yaml  # OpenAPI 3.0.3 specification
│   │   ├── API_TESTING.md # Testing guide
│   │   └── README.md     # API overview
│   └── prompt/           # Development prompts
├── tool/                  # Development tools
│   ├── serve_swagger.py  # Swagger UI server (Python)
│   ├── sync_symbols.py   # Symbol synchronization
│   ├── requirements.txt  # Python dependencies
│   └── README.md         # Tools documentation
├── swagger-ui.sh          # Quick launcher script
```

## Quick Start

### Prerequisites

- Python 3.8+ (recommended)
- pip (Python package manager)

### API Testing with Swagger UI

The project includes a comprehensive Swagger UI interface with CORS proxy for direct API testing.

#### Setup and Launch

```bash
# 1. Install dependencies (first time only)
pip install flask pyyaml requests

# 2. Start Swagger UI server
./swagger-ui.sh
# Or manually: python tool/serve_swagger.py

# 3. Open browser
# http://localhost:8888
```

#### Testing Process

**Direct API Testing with JWT Setup:**
1. Start Swagger UI server: `./swagger-ui.sh`
2. Open `http://localhost:8888` in your browser
3. Follow the JWT setup instructions on the page:
   - Login to `https://api.myinvestpilot.com` in another tab
   - Copy JWT token from browser Developer Tools → Cookies
   - Paste token in the JWT setup section
4. Test API endpoints directly through the local proxy!

#### Features

- 🧪 **Direct API Testing**: Test all endpoints directly in browser
- 🔄 **Strategy Type Testing**: Both Code and DSL strategy formats
- � **Request/Response Examples**: Clear examples for all operations
- � **Automatic Authentication**: Browser cookies forwarded through proxy
- 🌐 **CORS Solution**: Built-in proxy eliminates CORS restrictions
- 📊 **Real-time Testing**: Immediate feedback and response inspection
- 📋 **Complete Coverage**: All 10 endpoints fully testable
- � **Copy-Paste Ready**: Examples ready for curl or other tools

#### Documentation

- **API Overview**: `doc/api/README.md`
- **Testing Guide**: `doc/api/API_TESTING.md`
- **Authentication**: `doc/api/AUTHENTICATION.md`
- **Endpoints Summary**: `doc/api/API_ENDPOINTS.md`

### Development

```bash
# Start local development server
cd app && npm run dev
```

## Strategy Types

The API supports two types of trading strategies:

- **Code Strategy**: References pre-defined strategies by name with parameters
- **DSL Strategy**: Uses embedded DSL definitions with internal parameters

See `doc/api/README.md` for detailed API documentation.

## AI-Assisted Strategy Generation

The platform includes AI-powered strategy generation capabilities that convert natural language descriptions into executable DSL strategies.

### Features

- 🤖 **Natural Language Processing**: Convert trading ideas described in plain language (Chinese/English) to structured DSL
- 📝 **JSON Validation**: Validate and complete partial strategy definitions
- 🔄 **Mixed Content Support**: Combine JSON definitions with natural language instructions
- 🎯 **Multiple Prompt Versions**: Choose from different AI models optimized for various use cases

### API Documentation

For complete API documentation including endpoints, examples, and interactive testing, see:
- **OpenAPI Specification**: `doc/api/openapi.yaml`
- **Interactive Testing**: Run `npm run swagger:serve` and open http://localhost:8888

### Architecture Benefits

- **Focused Design**: Clean, simple API focused on strategy generation
- **Version Management**: Multiple prompt versions for different use cases and A/B testing
- **Extensible Foundation**: Easy to add new AI services when needed (portfolio optimization, risk analysis, etc.)
- **Modular Structure**: Each AI service has its own configuration and prompt management

## Architecture

### Portfolio Update Flow

```mermaid
sequenceDiagram
    participant CT as Cron Trigger (10min)
    participant BCT as Biday Cron Trigger
    participant U as User
    participant MP as myInvestStrategyPortfolio
    participant DB as Database
    participant MTS as myInvestTradeSignal
    participant K as Kafka
    participant IS as investStrategyService
    participant R2 as Cloudflare R2

    Note over CT,IS: Automated Update Process (Every 10 minutes)
    CT->>MP: Trigger update check
    MP->>DB: Query portfolios needing update
    Note over MP,DB: Filter conditions:<br/>1. is_deleted = false<br/>2. current_time > update_time<br/>3. (last_data_update_at is not today OR<br/>update_status != 'COMPLETED')<br/>4. If update_status = 'PENDING',<br/>check if last_update_attempt > 15 minutes ago
    DB-->>MP: Return portfolios
    loop For each portfolio has active subscribers
        alt update_status = 'PENDING'
            MP->>R2: Check for updated files
            R2-->>MP: Return file metadata
            alt Files updated
                MP->>DB: Update last_data_update_at and status to 'COMPLETED'
            else Files not updated
                MP->>DB: Update status to 'FAILED'
            end
        else update_status != 'PENDING'
            MP->>DB: Set status to 'PENDING', update last_update_attempt
            MP->>K: Send update message
        end
    end
    K->>IS: Consume update message
    IS->>R2: Update portfolio data
    IS->>R2: Store updated files

    Note over U,R2: Manual Update Process
    U->>MP: Trigger manual update
    MP->>K: Send update message
    K->>IS: Consume update message
    IS->>R2: Update portfolio data
    IS->>R2: Store updated files
    alt Update successful
        R2-->>U: User checks updated data
    else Update failed
        U->>MP: User may retry manually
    end

    Note over BCT,DB: Cleanup Unsubscribed Portfolios (Every 2 days)
    BCT->>MP: Trigger cleanup process
    MP->>DB: Query all portfolios with is_deleted = false
    DB-->>MP: Return non-deleted portfolios
    loop For each portfolio
        MP->>MTS: Get subscriber count
        MTS-->>MP: Return subscriber count
        alt Subscriber count = 0
            MP->>DB: Update is_deleted to true
        end
    end
```

### Portfolio Time Fly Flow

```mermaid
sequenceDiagram
    participant Client as myInvestPilotLandingPage
    participant Gateway as myInvestGateway
    participant Worker as myInvestStrategyPortfolio
    participant R2 as Cloudflare R2
    participant Kafka as Kafka
    participant Service as investStrategyService (fly.io)

    loop Until file is available
        Client->>Gateway: Request portfolio data
        Gateway->>Worker: Forward request
        Worker->>R2: Check for today's file
        alt File not found
            R2-->>Worker: File not found
            Worker->>Kafka: Send processing message
            Kafka->>Service: Consume message
            Service->>Service: Process portfolio data
            Service->>R2: Upload processed file
            Worker-->>Gateway: Return 404
            Gateway-->>Client: Return 404
            Note over Client: Wait 30 seconds
        else File found
            R2-->>Worker: Return file metadata
            Worker-->>Gateway: Return file download URL
            Gateway-->>Client: Return file download URL
        end
    end

    Client->>R2: Download file using URL
```

## Database Schema

```mermaid
erDiagram
    PORTFOLIOS ||--o{ STRATEGIES : uses
    PORTFOLIOS ||--o{ USER_PORTFOLIOS : has
    PORTFOLIOS {
        uuid id PK
        string name
        string code UK
        text description
        uuid strategy_id FK
        uuid capital_strategy_id FK
        text[] symbols
        date start_date
        string currency
        string market
        decimal commission
        time update_time
        jsonb parameters
        boolean is_official
        boolean is_deleted
        timestamp created_at
        timestamp updated_at
        timestamp last_data_update_at
        enum update_status
        timestamp last_update_attempt
    }
    STRATEGIES {
        uuid id PK
        string name UK
        enum type
        text description
        enum implementation_type
        text dsl_script
        jsonb parameters
        timestamp created_at
        timestamp updated_at
    }
    SYMBOLS {
        uuid id PK
        string symbol
        string name
        enum type
        string market
        string country
        string currency
        boolean is_deleted
        timestamp created_at
        timestamp updated_at
    }
    USER_PORTFOLIOS {
        uuid id PK
        string email
        uuid portfolio_id FK
        boolean is_deleted
        timestamp created_at
        timestamp updated_at
    }
```

## MQ Message Schema

The system supports two strategy formats in MQ messages:

### Code Strategy Format (Traditional)

```json
{
  "job_id": "37fc2ed4-add4-459f-a22b-e4c1e7bc4dcb",
  "timestamp": "2024-08-15T15:12:23.884Z",
  "action": "create_or_update",
  "portfolio_config": {
    "name": "A股1号",
    "code": "myinvestpilot_cn_1~2023",
    "description": "基于双均线策略的A股精选ETF组合",
    "strategy": {
      "name": "DualMovingAverageStrategy",
      "params": { "long_window": 22, "short_window": 11 }
    },
    "capital_strategy": {
      "name": "PercentCapitalStrategy",
      "params": { "percents": 20, "initial_capital": 100000 }
    },
    "symbols": [
      { "symbol": "159915", "name": "创业板ETF" },
      { "symbol": "159928", "name": "消费ETF" },
      { "symbol": "159929", "name": "医药ETF" },
      { "symbol": "159939", "name": "信息技术ETF" },
      { "symbol": "159941", "name": "纳指ETF" },
      { "symbol": "510500", "name": "中证500ETF" },
      { "symbol": "512100", "name": "中证1000ETF" },
      { "symbol": "512660", "name": "军工ETF" },
      { "symbol": "515180", "name": "红利ETF易方达" }
    ],
    "start_date": "2023-01-01",
    "end_date": "2024-08-15",
    "currency": "CNY",
    "market": "China",
    "commission": 0.0001,
    "update_time": "01:00:00",
    "is_official": true,
    "created_at": "2024-08-11T03:05:06.824Z",
    "updated_at": "2024-08-11T03:05:06.824Z",
    "last_data_update_at": null,
    "update_status": "PENDING"
  }
}
```

### DSL Strategy Format (Primitive)

```json
{
  "job_id": "f8a3c2d1-b5e7-4f9a-8c6d-1e2f3a4b5c6d",
  "timestamp": "2024-08-15T15:12:23.884Z",
  "action": "create_or_update",
  "portfolio_config": {
    "name": "A股1号(原语版)",
    "code": "myinvestpilot_cn_1_primitive",
    "description": "基于原语化双均线策略的A股精选ETF组合",
    "strategy_definition": {
      "trade_strategy": {
        "indicators": [
          { "id": "shortMA", "type": "SMA", "params": { "period": 11, "column": "Close" } },
          { "id": "longMA", "type": "SMA", "params": { "period": 22, "column": "Close" } }
        ],
        "signals": [
          {
            "id": "buy_signal",
            "type": "Crossover",
            "params": { "mode": "simple" },
            "inputs": [{ "ref": "shortMA" }, { "ref": "longMA" }]
          },
          {
            "id": "sell_signal",
            "type": "Crossunder",
            "params": { "mode": "simple" },
            "inputs": [{ "ref": "shortMA" }, { "ref": "longMA" }]
          }
        ],
        "outputs": {
          "buy_signal": "buy_signal",
          "sell_signal": "sell_signal"
        }
      },
      "capital_strategy": {
        "name": "PercentCapitalStrategy",
        "params": { "initial_capital": 100000, "percents": 20 }
      }
    },
    "symbols": [
      { "symbol": "159928", "name": "消费ETF" },
      { "symbol": "159929", "name": "医药ETF" },
      { "symbol": "510500", "name": "中证500ETF" },
      { "symbol": "159915", "name": "创业板ETF" },
      { "symbol": "159939", "name": "信息技术ETF" },
      { "symbol": "512100", "name": "中证1000ETF" },
      { "symbol": "512660", "name": "军工ETF" },
      { "symbol": "159941", "name": "纳指ETF" },
      { "symbol": "515180", "name": "红利ETF易方达" }
    ],
    "start_date": "2018-09-20",
    "end_date": "2024-08-15",
    "currency": "CNY",
    "market": "China",
    "commission": 0.0001,
    "update_time": "01:00:00",
    "is_official": true,
    "created_at": "2024-08-11T03:05:06.824Z",
    "updated_at": "2024-08-11T03:05:06.824Z",
    "last_data_update_at": null,
    "update_status": "PENDING"
  }
}
```

### Key Differences

- **Code Strategy**: Uses `strategy` + `capital_strategy` fields with predefined strategy classes
- **DSL Strategy**: Uses `strategy_definition` field with embedded primitive strategy configuration
- **Capital Strategy**: Code strategies reference external classes, DSL strategies embed capital strategy in `strategy_definition`
- **Flexibility**: DSL strategies allow custom indicator/signal combinations, Code strategies use predefined logic
