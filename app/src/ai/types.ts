/**
 * AI服务通用类型定义
 * 支持多种AI功能的扩展
 */

import { TradeStrategy } from '../types';


// AI服务类型枚举
export enum AiServiceType {
  STRATEGY_GENERATION = 'strategy_generation'
  // 未来可扩展：
  // PORTFOLIO_OPTIMIZATION = 'portfolio_optimization',
  // RISK_ANALYSIS = 'risk_analysis',
  // MARKET_ANALYSIS = 'market_analysis'
}

// 通用AI请求接口
export interface BaseAiRequest {
  userInput: string;
  promptVersion?: string;
  model?: string; // 未来支持多模型
}

// 通用AI响应接口
export interface BaseAiResponse {
  success: boolean;
  error?: string;
  metadata?: {
    promptVersion: string;
    model: string;
    processingTime?: number;
    tokensUsed?: number;
  };
}

// 策略生成相关类型
export interface StrategyGenerationRequest extends BaseAiRequest {
  // 策略生成特有的参数可以在这里扩展
  riskLevel?: 'low' | 'medium' | 'high';
  timeframe?: string;
}

export interface StrategyGenerationResponse extends BaseAiResponse {
  tradeStrategy?: TradeStrategy;
}

// Prompt版本信息
export interface PromptVersionInfo {
  name: string;
  description: string;
  file: string;
  stable: boolean;
  features: readonly string[];
  serviceType: AiServiceType;
}

// Prompt配置
export interface PromptConfig {
  serviceType: AiServiceType;
  versions: Record<string, PromptVersionInfo>;
  defaultVersion: string;
}
