-- Insert mainstream cryptocurrencies into the symbols table with generated UUIDs
INSERT INTO symbols (id, symbol, name, type, market, country, currency)
VALUES
    (gen_random_uuid(), 'BTC', 'BTC', 'CRYPTO', 'Binance', 'US', 'USD'),
    (gen_random_uuid(), 'ETH', 'ETH', 'CRYPTO', 'Binance', 'US', 'USD'),
    (gen_random_uuid(), 'XRP', 'XRP', 'CRYPTO', 'Binance', 'US', 'USD'),
    (gen_random_uuid(), 'LTC', 'LTC', 'CRYPTO', 'Binance', 'US', 'USD'),
    (gen_random_uuid(), 'BCH', 'BCH', 'CRYPTO', 'Binance', 'US', 'USD'),
    (gen_random_uuid(), 'ADA', 'ADA', 'CRYPTO', 'Binance', 'US', 'USD'),
    (gen_random_uuid(), 'DOT', 'DOT', 'CRYPTO', 'Binance', 'US', 'USD'),
    (gen_random_uuid(), 'BNB', 'BNB', 'CRYPTO', 'Binance', 'US', 'USD'),
    (gen_random_uuid(), 'LINK', 'LINK', 'CRYPTO', 'Binance', 'US', 'USD'),
    (gen_random_uuid(), 'XLM', 'XLM', 'CRYPTO', 'Binance', 'US', 'USD'),
    (gen_random_uuid(), 'SOL', 'SOL', 'CRYPTO', 'Binance', 'US', 'USD'),
    (gen_random_uuid(), 'DOGE', 'DOGE', 'CRYPTO', 'Binance', 'US', 'USD'),
    (gen_random_uuid(), 'TRX', 'TRX', 'CRYPTO', 'Binance', 'US', 'USD'),
    (gen_random_uuid(), 'AVAX', 'AVAX', 'CRYPTO', 'Binance', 'US', 'USD'),
    (gen_random_uuid(), 'UNI', 'UNI', 'CRYPTO', 'Binance', 'US', 'USD');