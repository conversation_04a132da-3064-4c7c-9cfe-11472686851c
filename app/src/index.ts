import { Env, QueueMessage, ExecutionContext, MessageBatch } from './types';
import { app } from './app';
import { cleanupUnsubscribedPortfolios, consumerProcess, producerProcess } from './workers/queueProcessor';

export default {
  fetch: (request: Request, env: Env, ctx: ExecutionContext) => {
    return app.fetch(request, env, ctx);
  },
  async scheduled(controller: ScheduledController, env: Env, ctx: ExecutionContext): Promise<void> {
    if (controller.cron === "*/10 * * * *") {
      ctx.waitUntil(producerProcess({env, executionCtx: ctx}));
    } else if (controller.cron === "0 0 */2 * *") {
      ctx.waitUntil(cleanupUnsubscribedPortfolios({env, executionCtx: ctx}));
    }
  },
  async queue(batch: MessageBatch<QueueMessage>, env: Env, ctx: ExecutionContext): Promise<void> {
    await consumerProcess(batch, env, ctx);
  },
};
