import { Redis } from "@upstash/redis/cloudflare";
import { Env, mqMessage, ExecutionContext } from '../types';

export const sendToMQ = async (env: Env, ctx: ExecutionContext, message: mqMessage) => {
  try {
    const redis = Redis.fromEnv(env);

    // Await the rpush operation
    ctx.waitUntil(redis.rpush(
      env.MQ_TOPIC_NAME,
      JSON.stringify(message)
    ));

		console.log(`Message sent to Redis list: ${env.MQ_TOPIC_NAME} with message: ${JSON.stringify(message)}`);
  } catch (error) {
    console.error('Failed to send message to Redis', {
      error,
      message,
      list: env.MQ_TOPIC_NAME
    });
  }
};
