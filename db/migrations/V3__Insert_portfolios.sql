-- First, we need to get the strategy IDs
WITH strategy_ids AS (
    SELECT id, name FROM strategies
)
INSERT INTO portfolios (
    id, name, code, description, strategy_id, capital_strategy_id,
    symbols, start_date, currency, market, commission, update_time,
    parameters, is_official
)
SELECT
    gen_random_uuid(),
    p.name,
    p.code,
    p.description,
    (SELECT id FROM strategy_ids WHERE name = (p.strategy::json->>'name')),
    (SELECT id FROM strategy_ids WHERE name = (p.capital_strategy::json->>'name')),
    p.symbols,
    p.start_date::date,
    p.currency,
    p.market,
    p.commission,
    p.update_time::time,
    jsonb_build_object(
        'strategy_params', (p.strategy::json->'params'),
        'capital_strategy_params', (p.capital_strategy::json->'params')
    ),
    true
FROM (
    VALUES
    ('美股1号', 'myinvestpilot_us_1', '基于特定止损与均线策略的美股杠杆ETF组合', 
     '{"name": "ChandelierExitMAStrategy", "params": {"n_atr": 60, "atr_multiplier": 4, "n_ma": 250}}',
     '{"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 20}}',
     ARRAY['FNGU', 'TQQQ', 'TECL', 'SOXL', 'TNA', 'CONL', 'LABU', 'FAS', 'EDC', 'TMF'],
     '2018-01-01', 'USD', 'US', 0.0001, '08:00'),
    
    ('美股1号A', 'myinvestpilot_us_1_a', '基于特定止损与均线策略的美股杠杆ETF组合', 
     '{"name": "ChandelierExitMAStrategy", "params": {"n_atr": 60, "atr_multiplier": 4, "n_ma": 250}}',
     '{"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 30}}',
     ARRAY['FNGU', 'TQQQ', 'SOXL', 'CONL'],
     '2015-01-01', 'USD', 'US', 0.0001, '08:00'),
    
    ('美股2号', 'myinvestpilot_us_2', '基于特定止损与均线策略的美股精选ETF组合', 
     '{"name": "ChandelierExitMAStrategy", "params": {"n_atr": 45, "atr_multiplier": 3, "n_ma": 120}}',
     '{"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 15}}',
     ARRAY['QQQ', 'XLK', 'XLY', 'VIG', 'ARKK', 'ICLN', 'FTEC', 'ESGU'],
     '2018-01-01', 'USD', 'US', 0.0001, '08:00'),
    
    ('美股3号', 'myinvestpilot_us_3', '基于特定止损与均线策略的美股纳指三倍做多ETF组合', 
     '{"name": "ChandelierExitMAStrategy", "params": {"n_atr": 60, "atr_multiplier": 4, "n_ma": 250}}',
     '{"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 99}}',
     ARRAY['TQQQ'],
     '2018-01-01', 'USD', 'US', 0.0001, '08:00'),
    
    ('美股3号A', 'myinvestpilot_us_3_a', '基于特定止损与均线策略的美股纳指三倍做多ETF组合', 
     '{"name": "ChandelierExitMAStrategy", "params": {"n_atr": 68, "atr_multiplier": 4, "n_ma": 28}}',
     '{"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 95}}',
     ARRAY['TQQQ'],
     '2018-01-01', 'USD', 'US', 0.0001, '08:00'),
    
    ('美股4号', 'myinvestpilot_us_4', '基于买入持有定投策略的美股行业ETF组合', 
     '{"name": "BuyHoldStrategy", "params": {}}',
     '{"name": "AnnualFixedInvestmentStrategy", "params": {"initial_capital": 100000, "annual_investment": 10000, "percents": 10}}',
     ARRAY['SPY', 'XBI', 'IHI', 'SCHD', 'IJR', 'XLP', 'XLY', 'XLE', 'XLI', 'XLF'],
     '2010-01-01', 'USD', 'US', 0.0001, '08:00'),
    
    ('美股全球', 'myinvestpilot_us_global', '基于双均线策略的美股全球ETF组合', 
     '{"name": "DualMovingAverageStrategy", "params": {"short_window": 11, "long_window": 22}}',
     '{"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 20}}',
     ARRAY['SPY', 'QQQ', 'CHAU', 'INDL', 'EWJ', 'EWG', 'GBTC', 'GLD', 'GUNR', 'PDBC', 'ICLN', 'XLE', 'LTL'],
     '2018-01-01', 'USD', 'US', 0.0001, '08:00'),
    
    ('A股1号', 'myinvestpilot_cn_1', '基于双均线策略的A股精选ETF组合', 
     '{"name": "DualMovingAverageStrategy", "params": {"short_window": 11, "long_window": 22}}',
     '{"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 20}}',
     ARRAY['159928', '159929', '510500', '159915', '159939', '512100', '512660', '159941', '515180'],
     '2018-09-20', 'CNY', 'China', 0.0001, '01:00'),
    
    ('A股2号', 'myinvestpilot_cn_2', '基于双均线策略的A股创业板ETF组合', 
     '{"name": "DualMovingAverageStrategy", "params": {"short_window": 20, "long_window": 26}}',
     '{"name": "SimplePercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 95}}',
     ARRAY['159915'],
     '2018-09-20', 'CNY', 'China', 0.0001, '01:00'),
    
    ('A股3号', 'myinvestpilot_cn_3', '基于双均线策略的A股ETF组合', 
     '{"name": "DualMovingAverageStrategy", "params": {"short_window": 11, "long_window": 22}}',
     '{"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 10}}',
     ARRAY['159805', '159869', '159901', '159902', '159915', '159920', '159928', '159929', '159930', '159931', '159936', '159938', '159939', '159941', '159949', '159995', '160416', '161039', '161907', '162411', '164906', '501018', '501029', '501050', '502010', '510170', '510180', '510300', '510500', '510660', '510880', '510900', '511220', '512010', '512070', '512100', '512200', '512580', '512660', '512800', '512880', '513030', '513050', '513500', '513520', '515070', '515180', '515450', '515880', '516510', '516520', '517090', '517180'],
     '2018-09-20', 'CNY', 'China', 0.0001, '01:00'),
    
    ('A股全球', 'myinvestpilot_cn_global', '基于双均线策略的A股全球ETF组合', 
     '{"name": "DualMovingAverageStrategy", "params": {"short_window": 12, "long_window": 24}}',
     '{"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 20}}',
     ARRAY['501018', '510170', '510300', '159915', '513500', '513030', '159920', '159941', '513520', '513080', '513380', '513730', '164824', '159329'],
     '2018-09-20', 'CNY', 'China', 0.0001, '01:00'),
    
    ('A股全球A', 'myinvestpilot_cn_global_old', '基于双均线策略的A股全球ETF组合', 
     '{"name": "DualMovingAverageStrategy", "params": {"short_window": 12, "long_window": 24}}',
     '{"name": "PercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 20}}',
     ARRAY['501018', '510170', '510300', '159915', '513500', '513030', '159920', '159941', '513520', '513080', '159509', '513380', '513730'],
     '2018-09-20', 'CNY', 'China', 0.0001, '01:00'),
    
    ('A股QQQ号', 'myinvestpilot_cn_qqq', '基于双均线策略的A股纳指ETF组合', 
     '{"name": "DualMovingAverageStrategy", "params": {"short_window": 10, "long_window": 25}}',
     '{"name": "SimplePercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 95}}',
     ARRAY['159941'],
     '2018-09-20', 'CNY', 'China', 0.0001, '01:00'),
    
    ('加密币1号', 'myinvestpilot_cc_1', '基于特定止损与均线策略的加密币组合', 
     '{"name": "ChandelierExitMAStrategy", "params": {"n_atr": 60, "atr_multiplier": 4, "n_ma": 250}}',
     '{"name": "SimplePercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 20}}',
     ARRAY['BTC', 'ETH', 'BNB', 'SOL', 'XRP', 'DOGE', 'ADA', 'TRX', 'AVAX', 'LINK'],
     '2018-01-01', 'USD', 'US', 0.001, '04:00'),
    
    ('加密币2号', 'myinvestpilot_cc_2', '基于特定止损与均线策略的BTC组合', 
     '{"name": "ChandelierExitMAStrategy", "params": {"n_atr": 30, "atr_multiplier": 2, "n_ma": 40}}',
     '{"name": "SimplePercentCapitalStrategy", "params": {"initial_capital": 100000, "percents": 99}}',
     ARRAY['BTC'],
     '2018-01-01', 'USD', 'US', 0.001, '04:00')
) AS p(name, code, description, strategy, capital_strategy, symbols, start_date, currency, market, commission, update_time);
