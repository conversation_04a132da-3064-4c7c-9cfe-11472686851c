import {
	ExecutionContext,
	ScheduledController,
	MessageBatch
} from '@cloudflare/workers-types';

export { ExecutionContext, ScheduledController, MessageBatch };

export interface DBContext {
	env: Env;
	executionCtx: ExecutionContext;
}

export enum PortfolioUpdateStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export interface Env {
  SERVICE_NAME: string
  BASELIME_API_KEY: string
  UPSTASH_KAFKA_REST_URL: string
  UPSTASH_KAFKA_REST_USERNAME: string
  UPSTASH_KAFKA_REST_PASSWORD: string
  MQ_TOPIC_NAME: string
	UPSTASH_REDIS_REST_URL: string
	UPSTASH_REDIS_REST_TOKEN: string
  DB_URL: string
  BUCKET: R2Bucket
  HYPERDRIVE: Hyperdrive
  QUEUE: Queue
  TRADE_SIGNAL: Fetcher
  OPENAI_API_KEY: string
  // AI Gateway配置 (可选，如果不设置则直连OpenAI)
  AI_GATEWAY_URL?: string
}

export interface QueueMessage {
  portfolio_code: string
}

export interface SymbolSearchResult {
  symbol: string;
  name: string;
  type: string;
  market: string; // It's Exchange!
  country: string;
  currency: string;
}

export interface SearchSymbolsResponse {
  results: SymbolSearchResult[];
}

// 基础类型定义
export type DynamicRecord = Record<string, any>;

// 市场指标定义
export interface MarketIndicator {
  code: string;
  start_date?: string;
  end_date?: string;
  [key: string]: any;
}

export interface Transformer {
  name: string;
  type: string;
  params: DynamicRecord;
  [key: string]: any;
}

export interface MarketIndicators {
  indicators?: MarketIndicator[];
  transformers?: Transformer[];
  [key: string]: any;
}

// 交易策略定义
export interface Indicator {
  id: string;
  type: string;
  params?: DynamicRecord;
  [key: string]: any;
}

export interface Signal {
  id: string;
  type: string;
  epsilon?: number;
  params?: DynamicRecord;
  inputs?: any[]; // 完全动态的输入结构
  [key: string]: any;
}

export interface IndicatorOutput {
  id: string;
  output_name: string;
  [key: string]: any;
}

export interface MarketIndicatorOutput {
  market: string;
  transformer: string;
  output_name: string;
  [key: string]: any;
}

export interface TradeStrategyOutputs {
  buy_signal: string;
  sell_signal: string;
  indicators?: IndicatorOutput[];
  market_indicators?: MarketIndicatorOutput[];
  [key: string]: any;
}

export interface TradeStrategy {
  indicators?: Indicator[];
  signals: Signal[];
  outputs: TradeStrategyOutputs;
  [key: string]: any;
}

// 资金策略定义
export interface CapitalStrategy {
  name: string;
  params: DynamicRecord;
  [key: string]: any;
}

// 完整策略定义
export interface StrategyDefinition {
  market_indicators?: MarketIndicators;
  trade_strategy: TradeStrategy;
  capital_strategy: CapitalStrategy;
  [key: string]: any;
}

// 用于MQ消息和API响应的DSL策略定义格式
export interface StrategyDefinitionForMQ {
  market_indicators?: MarketIndicators; // 市场指标（可选）
  trade_strategy: TradeStrategy;    // 交易策略的DSL定义
  capital_strategy: StrategyConfig;    // 资金策略 { name, params }
}

// 用于API输入的DSL策略定义格式
export interface StrategyDefinitionForApiInput {
  market_indicators?: MarketIndicators;  // 添加市场指标支持
  trade_strategy: TradeStrategy;
  capital_strategy: StrategyConfig;
}

// 代码策略组合的创建请求
export interface CreatePortfolioRequestCodeStrategy {
  name: string;
  description: string;
  symbols: string[];
  start_date: string;
  currency: string;
  market: string; // It's Country!
  commission: number;
  update_time: string;
  strategy: {
    name: string;
    params: Record<string, number>;
  };
  capital_strategy: {
    name: string;
    params: Record<string, number>;
  };
  strategy_definition?: never;
}

// DSL策略组合的创建请求
export interface CreatePortfolioRequestDslStrategy {
  name: string;
  description: string;
  symbols: string[];
  start_date: string;
  currency: string;
  market: string; // It's Country!
  commission: number;
  update_time: string;
  strategy_definition: StrategyDefinitionForApiInput;
  strategy?: never;
  capital_strategy?: never;
}

// 联合类型：支持两种策略格式的创建请求
export type CreatePortfolioRequest = CreatePortfolioRequestCodeStrategy | CreatePortfolioRequestDslStrategy;

// 代码策略组合的更新请求
export interface UpdatePortfolioRequestCodeStrategy {
  name?: string;
  description?: string;
  symbols?: string[];
  start_date?: string;
  currency?: string;
  market?: string; // It's Country!
  commission?: number;
  update_time?: string;
  strategy?: {
    name: string;
    params: Record<string, number>;
  };
  capital_strategy?: {
    name: string;
    params: Record<string, number>;
  };
  strategy_definition?: never;
}

// DSL策略组合的更新请求
export interface UpdatePortfolioRequestDslStrategy {
  name?: string;
  description?: string;
  symbols?: string[];
  start_date?: string;
  currency?: string;
  market?: string; // It's Country!
  commission?: number;
  update_time?: string;
  strategy_definition?: StrategyDefinitionForApiInput;
  strategy?: never;
  capital_strategy?: never;
}

// 联合类型：支持两种策略格式的更新请求
export type UpdatePortfolioRequest = UpdatePortfolioRequestCodeStrategy | UpdatePortfolioRequestDslStrategy;

export interface SymbolInfo {
  symbol: string;
  name: string;
}

export interface StrategyConfig {
  name: string;
  params: Record<string, any>;
}

// MQ消息体中，代表"代码策略"组合的配置 (与当前线上一致)
export interface PortfolioConfigCodeStrategyMQ {
  name: string;
  code: string;
  description: string;
  strategy: StrategyConfig;           // 代码交易策略 { name, params }
  capital_strategy: StrategyConfig;   // 代码资金策略 { name, params }
  symbols: SymbolInfo[];
  start_date: string;
  currency: string;
  market: string; // It's Country!
  commission: number;
  update_time: string;
  is_official: boolean;
  created_at: string;
  updated_at: string;
  last_data_update_at: string | null;
  update_status: PortfolioUpdateStatus;
}

// MQ消息体中，代表"DSL策略"组合的配置 (新格式)
export interface PortfolioConfigDslStrategyMQ {
  name: string;
  code: string;
  description: string;
  strategy_definition: StrategyDefinitionForMQ; // 包含 trade_strategy (DSL) 和 capital_strategy ({name, params})
  symbols: SymbolInfo[];
  start_date: string;
  currency: string;
  market: string; // It's Country!
  commission: number;
  update_time: string;
  is_official: boolean;
  created_at: string;
  updated_at: string;
  last_data_update_at: string | null;
  update_status: PortfolioUpdateStatus;
}

// 保持向后兼容的 PortfolioConfig 类型别名
export type PortfolioConfig = PortfolioConfigCodeStrategyMQ;

export interface mqMessage {
  job_id: string;
  timestamp: string;
  action: 'create_or_update';
  portfolio_config: PortfolioConfigCodeStrategyMQ | PortfolioConfigDslStrategyMQ; // 联合类型
}

export interface Portfolio {
  code: string;
  name: string;
  description: string;
	market : string;  // It's Country!
	currency: string;
	start_date: string;
	is_subscribable?: boolean;
	is_deleted?: boolean;
}

export interface StrategyGroup {
  strategy_id: string;
  strategy_name: string;
  strategy_description: string;
  official_portfolios: Portfolio[];
}

export interface MarketGroup {
  market: string; // It's Country!
  official_portfolios: Portfolio[];
}

// 从数据库获取的组合数据，用于消息构造时的判断
export interface PortfolioDataFromDB {
  id: string;
  name: string;
  code: string;
  description: string;
  strategy_id: string;
  strategy_name: string;
  strategy_implementation_type: 'CODE' | 'DSL';
  capital_strategy_id: string;
  capital_strategy_name: string;
  symbols: string[];
  start_date: string;
  currency: string;
  market: string;
  commission: number;
  update_time: string;
  parameters_from_db: {
    strategy_params?: Record<string, any>;
    capital_strategy_params?: Record<string, any>;
    trade_strategy_dsl?: TradeStrategy;
    market_indicators?: MarketIndicators;
  };
  is_official: boolean;
  created_at: string;
  updated_at: string;
  last_data_update_at: string | null;
  update_status: PortfolioUpdateStatus;
  symbols_json: SymbolInfo[];
}
