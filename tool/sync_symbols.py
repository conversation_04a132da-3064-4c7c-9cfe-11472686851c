import os
import logging
from io import StringIO
import requests
import pandas as pd
from dotenv import load_dotenv
from sqlalchemy import create_engine, Column, String, Boolean, DateTime, func, Enum
from sqlalchemy.dialects.postgresql import UUID, insert
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import tushare as ts
import time
import enum

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Database connection
DB_URL = os.getenv('DATABASE_URL')
engine = create_engine(DB_URL)

# Constants
ALPHAVANTAGE_API_KEY = os.getenv('ALPHAVANTAGE_API_KEY')
TUSHARE_API_TOKEN = os.getenv('TUSHARE_API_TOKEN')

# Define the Symbol model
Base = declarative_base()

class SymbolType(enum.Enum):
    STOCK = 'STOCK'
    ETF = 'ETF'

class Symbol(Base):
    __tablename__ = 'symbols'
    __table_args__ = {'schema': 'strategy'}

    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    symbol = Column(String(50), nullable=False)
    name = Column(String(255), nullable=False)
    type = Column(Enum(SymbolType), nullable=False)
    market = Column(String(50), nullable=False)
    country = Column(String(50))
    currency = Column(String(3), nullable=False)
    is_deleted = Column(Boolean, default=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

# Create tables
Base.metadata.create_all(engine)

# Create a session
Session = sessionmaker(bind=engine)

class AlphavantageClient:
    def __init__(self, av_key):
        self._av_key = av_key

    def _get_listing_status(self, ticker_type):
        CSV_URL = f'https://www.alphavantage.co/query?function=LISTING_STATUS&apikey={self._av_key}'

        with requests.Session() as s:
            download = s.get(CSV_URL)
            decoded_content = download.content.decode('utf-8')
            stock_symbols = pd.read_csv(StringIO(decoded_content))
            
            stock_symbols = stock_symbols.rename(columns={'ipoDate': 'list_date'})
            stock_symbols = stock_symbols[stock_symbols['assetType'] == ticker_type]
            stock_symbols['exchange'] = stock_symbols['exchange'].apply(lambda x: 'NYSE' if 'NYSE' in x else x)

        return stock_symbols[['symbol', 'name', 'exchange', 'list_date']]

    def get_stock_ticker_symbols(self):
        return self._get_listing_status('Stock')

    def get_etf_ticker_symbols(self):
        return self._get_listing_status('ETF')

class TSClient:
    def __init__(self):
        self.__pro = ts.pro_api(TUSHARE_API_TOKEN)

    def get_stock_ticker_symbols(self):
        stock_fields = [
            "ts_code", "symbol", "name", "market", "list_date", "exchange",
            "industry", "area", "fullname", "enname", "cnspell", "curr_type", "list_status"
        ]
        sh = self.__pro.stock_basic(exchange='SSE', list_status='L', fields=stock_fields)
        sz = self.__pro.stock_basic(exchange='SZSE', list_status='L', fields=stock_fields)
        return pd.concat([sh, sz])
    
    def get_etf_ticker_symbols(self):
        etf_fields = [
            "ts_code", "name", "management", "custodian", "fund_type",
            "found_date", "list_date", "issue_date", "delist_date", "issue_amount",
            "m_fee", "c_fee", "duration_year", "p_value", "min_amount", "exp_return",
            "benchmark", "status", "invest_type", "type", "trustee", "purc_startdate",
            "redm_startdate", "market"
        ]
        fund = self.__pro.fund_basic(market='E', status='L', fields=etf_fields)
        return fund[fund['invest_type'] == '被动指数型']

av_client = AlphavantageClient(ALPHAVANTAGE_API_KEY)
ts_client = TSClient()

def sync_us_symbols():
    logging.info("Starting to sync US stock symbols...")
    start_time = time.time()
    stocks = av_client.get_stock_ticker_symbols()
    etfs = av_client.get_etf_ticker_symbols()
    
    symbols = []
    for df in [stocks, etfs]:
        symbol_type = SymbolType.STOCK if df is stocks else SymbolType.ETF
        logging.info(f"Processing US {symbol_type.name}s: {len(df)} symbols")
        for i, (_, row) in enumerate(df.iterrows(), 1):
            symbols.append({
                'symbol': row['symbol'],
                'name': row['name'],
                'type': symbol_type,
                'market': row['exchange'],
                'country': 'US',
                'currency': 'USD',
            })
            if i % 1000 == 0:
                logging.info(f"Processed {i}/{len(df)} US {symbol_type.name}s")
    
    upsert_symbols(symbols)
    end_time = time.time()
    logging.info(f"Finished syncing US symbols. Total time: {end_time - start_time:.2f} seconds")

def sync_cn_symbols():
    logging.info("Starting to sync CN stock symbols...")
    start_time = time.time()
    stocks = ts_client.get_stock_ticker_symbols()
    etfs = ts_client.get_etf_ticker_symbols()
    
    symbols = []
    for df in [stocks, etfs]:
        symbol_type = SymbolType.STOCK if df is stocks else SymbolType.ETF
        logging.info(f"Processing CN {symbol_type.name}s: {len(df)} symbols")
        for i, (_, row) in enumerate(df.iterrows(), 1):
            symbols.append({
                'symbol': row['ts_code'].split('.')[0],
                'name': row['name'],
                'type': symbol_type,
                'market': row['ts_code'].split('.')[1],
                'country': 'China',
                'currency': 'CNY',
            })
            if i % 1000 == 0:
                logging.info(f"Processed {i}/{len(df)} CN {symbol_type.name}s")
    
    upsert_symbols(symbols)
    end_time = time.time()
    logging.info(f"Finished syncing CN symbols. Total time: {end_time - start_time:.2f} seconds")

def upsert_symbols(symbols):
    start_time = time.time()
    logging.info(f"Starting to upsert {len(symbols)} symbols...")
    session = Session()
    try:
        stmt = insert(Symbol).values(symbols)
        stmt = stmt.on_conflict_do_update(
            index_elements=['symbol', 'market', 'currency'],
            set_={
                'name': stmt.excluded.name,
                'type': stmt.excluded.type,
                'country': stmt.excluded.country,
                'is_deleted': False,
                'updated_at': func.now(),
            }
        )
        session.execute(stmt)
        session.commit()
    except Exception as e:
        session.rollback()
        logging.error(f"Error upserting symbols: {str(e)}")
    finally:
        session.close()
    end_time = time.time()
    logging.info(f"Finished upserting {len(symbols)} symbols. Time taken: {end_time - start_time:.2f} seconds")

def main():
    overall_start_time = time.time()
    sync_us_symbols()
    sync_cn_symbols()
    overall_end_time = time.time()
    logging.info(f"Completed all symbol synchronization. Total time: {overall_end_time - overall_start_time:.2f} seconds")

if __name__ == "__main__":
    main()