-- Migration: Add default UUID to strategies, portfolios, and symbols tables

-- Step 1: Enable the uuid-ossp extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Step 2: Add default UUID to strategies table
ALTER TABLE strategies
ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- Step 3: Add default UUID to portfolios table
ALTER TABLE portfolios
ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- Step 4: Add default UUID to symbols table
ALTER TABLE symbols
ALTER COLUMN id SET DEFAULT gen_random_uuid();

-- Step 5: Add comments to explain the changes
COMMENT ON COLUMN strategies.id IS 'Primary key with default UUID generation';
COMMENT ON COLUMN portfolios.id IS 'Primary key with default UUID generation';
COMMENT ON COLUMN symbols.id IS 'Primary key with default UUID generation';

-- Step 6: Verify the changes
SELECT table_name, column_name, column_default
FROM information_schema.columns
WHERE table_name IN ('strategies', 'portfolios', 'symbols')
  AND column_name = 'id';