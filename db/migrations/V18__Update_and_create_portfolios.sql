-- 更新美股4号组合为美股定投1号
UPDATE portfolios
SET name = '美股定投1号',
    code = 'myinvestpilot_us_dip_1',
    description = '基于买入持有年度定投策略的ETF组合',
    update_time = '09:00',
    parameters = '{"investment_amount": 12000, "investment_frequency": "y", "percents": 95, "initial_capital": 100000}',
    is_subscribable = false
WHERE code = 'myinvestpilot_us_4';

-- 创建美股定投2号（月定投QQQ）
INSERT INTO portfolios (
    id, name, code, description, strategy_id, capital_strategy_id,
    symbols, start_date, currency, market, commission, update_time,
    parameters, is_official, created_at, updated_at, is_subscribable
)
VALUES (
    gen_random_uuid(),
    '美股定投2号',
    'myinvestpilot_us_dip_2',
    '基于买入持有月度定投策略的 QQQ ETF组合',
    (SELECT id FROM strategies WHERE name = 'BuyHoldStrategy'),
    (SELECT id FROM strategies WHERE name = 'FixedInvestmentStrategy'),
    ARRAY['QQQ'],
    '2015-01-01',
    'USD',
    'US',
    0.0001,
    '09:00',
    '{"investment_amount": 1000, "investment_frequency": "m", "percents": 95, "initial_capital": 100000}',
    true,
    NOW(),
    NOW(),
    false
);

-- 创建A股定投1号（年定投沪深300）
INSERT INTO portfolios (
    id, name, code, description, strategy_id, capital_strategy_id,
    symbols, start_date, currency, market, commission, update_time,
    parameters, is_official, created_at, updated_at, is_subscribable
)
VALUES (
    gen_random_uuid(),
    'A股定投1号',
    'myinvestpilot_cn_dip_1',
    '基于买入持有年度定投策略的沪深300ETF组合',
    (SELECT id FROM strategies WHERE name = 'BuyHoldStrategy'),
    (SELECT id FROM strategies WHERE name = 'FixedInvestmentStrategy'),
    ARRAY['510300'],
    '2015-01-01',
    'CNY',
    'CN',
    0.0003,
    '01:00',
    '{"investment_amount": 12000, "investment_frequency": "y", "percents": 95, "initial_capital": 100000}',
    true,
    NOW(),
    NOW(),
    false
);

-- 创建A股定投2号（月定投沪深300）
INSERT INTO portfolios (
    id, name, code, description, strategy_id, capital_strategy_id,
    symbols, start_date, currency, market, commission, update_time,
    parameters, is_official, created_at, updated_at, is_subscribable
)
VALUES (
    gen_random_uuid(),
    'A股定投2号',
    'myinvestpilot_cn_dip_2',
    '基于买入持有月度定投策略的沪深300ETF组合',
    (SELECT id FROM strategies WHERE name = 'BuyHoldStrategy'),
    (SELECT id FROM strategies WHERE name = 'FixedInvestmentStrategy'),
    ARRAY['510300'],
    '2015-01-01',
    'CNY',
    'CN',
    0.0003,
    '01:00',
    '{"investment_amount": 1000, "investment_frequency": "m", "percents": 95, "initial_capital": 100000}',
    true,
    NOW(),
    NOW(),
    false
);

-- 创建加密币定投1号（年定投BTC）
INSERT INTO portfolios (
    id, name, code, description, strategy_id, capital_strategy_id,
    symbols, start_date, currency, market, commission, update_time,
    parameters, is_official, created_at, updated_at, is_subscribable
)
VALUES (
    gen_random_uuid(),
    '加密币定投1号',
    'myinvestpilot_cc_dip_1',
    '基于买入持有年度定投策略的比特币组合',
    (SELECT id FROM strategies WHERE name = 'BuyHoldStrategy'),
    (SELECT id FROM strategies WHERE name = 'FixedInvestmentStrategy'),
    ARRAY['BTC'],
    '2018-01-01',
    'USD',
    'CRYPTO',
    0.001,
    '01:00',
    '{"investment_amount": 2400000, "investment_frequency": "y", "percents": 95, "initial_capital": 10000000}',
    true,
    NOW(),
    NOW(),
    false
);

-- 创建加密币定投2号（月定投BTC）
INSERT INTO portfolios (
    id, name, code, description, strategy_id, capital_strategy_id,
    symbols, start_date, currency, market, commission, update_time,
    parameters, is_official, created_at, updated_at, is_subscribable
)
VALUES (
    gen_random_uuid(),
    '加密币定投2号',
    'myinvestpilot_cc_dip_2',
    '基于买入持有月度定投策略的比特币组合',
    (SELECT id FROM strategies WHERE name = 'BuyHoldStrategy'),
    (SELECT id FROM strategies WHERE name = 'FixedInvestmentStrategy'),
    ARRAY['BTC'],
    '2018-01-01',
    'USD',
    'CRYPTO',
    0.001,
    '01:00',
    '{"investment_amount": 200000, "investment_frequency": "m", "percents": 95, "initial_capital": 10000000}',
    true,
    NOW(),
    NOW(),
    false
);
