#!/usr/bin/env python3

"""
简单的AI策略生成测试脚本
用于测试不同prompt版本的响应质量
"""

import argparse
import json
import time
import webbrowser
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

import requests

# 测试用例
TEST_CASES = [
    {
        'name': '基础双均线策略（中文）',
        'input': '我想要一个双均线策略，短期均线10天，长期均线30天，当短期均线上穿长期均线时买入，下穿时卖出'
    },
    {
        'name': 'Basic Dual MA Strategy (English)',
        'input': 'I want a dual moving average strategy with 10-day short MA and 30-day long MA, buy when short MA crosses above long MA, sell when it crosses below'
    },
    {
        'name': 'RSI策略',
        'input': 'Create an RSI strategy with 14-period RSI, buy when RSI is below 30 (oversold), sell when RSI is above 70 (overbought)'
    },
    {
        'name': '复杂多指标策略',
        'input': '创建一个综合策略：使用MACD判断趋势方向，RSI确认超买超卖，布林带判断价格位置，只有当MACD金叉且RSI小于70且价格接近布林带下轨时才买入'
    },
    {
        'name': 'JSON补全',
        'input': '{"indicators": [{"id": "rsi14", "type": "RSI", "params": {"period": 14, "column": "Close"}}], "signals": [], "outputs": {}}'
    },
    {
        'name': '混合内容',
        'input': '{"indicators": [{"id": "sma_short", "type": "SMA", "params": {"period": 10, "column": "Close"}}, {"id": "sma_long", "type": "SMA", "params": {"period": 30, "column": "Close"}}], "signals": [], "outputs": {}}\n\n请帮我完善上面的策略，添加金叉死叉信号'
    }
]

# 默认配置
DEFAULT_CONFIG = {
    'base_url': 'http://localhost:8787/strategy_portfolio',
    'versions': ['v1.0', 'v1.1'],
    'auth_email': '<EMAIL>',
    'timeout': 30
}


def call_api(user_input: str, prompt_version: str, config: Dict) -> Dict[str, Any]:
    """调用AI策略生成API"""
    url = f"{config['base_url']}/ai/strategy/generate"
    headers = {'Content-Type': 'application/json'}
    
    if config['auth_email']:
        headers['x-auth-user'] = config['auth_email']
    
    payload = {
        'userInput': user_input,
        'promptVersion': prompt_version
    }
    
    try:
        response = requests.post(
            url, 
            json=payload, 
            headers=headers, 
            timeout=config['timeout']
        )
        
        return {
            'success': response.ok and response.json().get('success', False),
            'status_code': response.status_code,
            'data': response.json(),
            'error': None
        }
    except Exception as e:
        return {
            'success': False,
            'status_code': None,
            'data': None,
            'error': str(e)
        }


def run_test(test_case: Dict, version: str, config: Dict) -> Dict[str, Any]:
    """运行单个测试"""
    print(f"🧪 测试: {test_case['name']} ({version})")
    
    start_time = time.time()
    result = call_api(test_case['input'], version, config)
    duration = int((time.time() - start_time) * 1000)  # 转换为毫秒
    
    return {
        'test_case': test_case['name'],
        'version': version,
        'success': result['success'],
        'duration': duration,
        'response': result['data'],
        'error': result['error']
    }


def save_results_to_file(results: List[Dict], config: Dict) -> str:
    """保存测试结果到JSON文件"""
    script_dir = Path(__file__).parent
    output_dir = script_dir / 'test-results'
    output_dir.mkdir(exist_ok=True)

    timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
    filename = output_dir / f'ai-test-{timestamp}.json'

    # 提取成功的策略结果
    successful_strategies = []
    for result in results:
        if result['success'] and result['response'] and result['response'].get('tradeStrategy'):
            successful_strategies.append({
                'test_case': result['test_case'],
                'version': result['version'],
                'trade_strategy': result['response']['tradeStrategy'],
                'duration': result['duration'],
                'timestamp': datetime.now().isoformat()
            })

    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(successful_strategies, f, ensure_ascii=False, indent=2)

    print(f"💾 策略结果已保存: {filename}")
    print(f"📊 成功策略数量: {len(successful_strategies)}")

    return str(filename)


def generate_html_report(results: List[Dict], config: Dict) -> str:
    """生成HTML报告"""
    # 确保输出目录存在（相对于脚本所在目录）
    script_dir = Path(__file__).parent
    output_dir = script_dir / 'test-results'
    output_dir.mkdir(exist_ok=True)
    
    # 生成文件名
    timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
    filename = output_dir / f'ai-test-{timestamp}.html'
    
    # 计算统计信息
    total_tests = len(results)
    success_count = sum(1 for r in results if r['success'])
    avg_duration = int(sum(r['duration'] for r in results) / total_tests) if total_tests > 0 else 0
    
    # 按测试用例分组
    test_cases_results = {}
    for result in results:
        test_name = result['test_case']
        if test_name not in test_cases_results:
            test_cases_results[test_name] = []
        test_cases_results[test_name].append(result)
    
    # 生成HTML内容
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI策略生成测试报告</title>
    <style>
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; margin: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; }}
        .header {{ background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .test-case {{ background: white; border-radius: 8px; margin-bottom: 20px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .test-header {{ background: #f8f9fa; padding: 15px; border-bottom: 1px solid #e9ecef; }}
        .test-title {{ font-size: 1.2em; font-weight: bold; margin-bottom: 5px; }}
        .test-input {{ color: #6c757d; font-style: italic; word-break: break-word; }}
        .versions {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 15px; padding: 15px; }}
        .version {{ border: 1px solid #e9ecef; border-radius: 4px; padding: 15px; }}
        .version.success {{ border-color: #28a745; background: #f8fff9; }}
        .version.failure {{ border-color: #dc3545; background: #fff8f8; }}
        .version-header {{ font-weight: bold; margin-bottom: 10px; }}
        .success {{ color: #28a745; }}
        .failure {{ color: #dc3545; }}
        .response {{ background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 10px; font-family: monospace; font-size: 0.9em; max-height: 300px; overflow-y: auto; cursor: pointer; white-space: pre-wrap; }}
        .response:hover {{ background: #e9ecef; }}
        .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-bottom: 20px; }}
        .stat {{ background: white; padding: 15px; border-radius: 8px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .stat-value {{ font-size: 2em; font-weight: bold; color: #007bff; }}
        .stat-label {{ color: #6c757d; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI策略生成测试报告</h1>
            <p><strong>测试时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>测试版本:</strong> {', '.join(config['versions'])}</p>
            <p><strong>API地址:</strong> {config['base_url']}</p>
        </div>

        <div class="stats">
            <div class="stat">
                <div class="stat-value">{len(TEST_CASES)}</div>
                <div class="stat-label">测试用例</div>
            </div>
            <div class="stat">
                <div class="stat-value">{len(config['versions'])}</div>
                <div class="stat-label">测试版本</div>
            </div>
            <div class="stat">
                <div class="stat-value">{success_count}</div>
                <div class="stat-label">成功次数</div>
            </div>
            <div class="stat">
                <div class="stat-value">{avg_duration}ms</div>
                <div class="stat-label">平均耗时</div>
            </div>
        </div>
"""
    
    # 添加每个测试用例的结果
    for test_case in TEST_CASES:
        test_name = test_case['name']
        test_input = test_case['input']
        test_results = test_cases_results.get(test_name, [])
        
        html_content += f"""
        <div class="test-case">
            <div class="test-header">
                <div class="test-title">{test_name}</div>
                <div class="test-input">{test_input}</div>
            </div>
            <div class="versions">
"""
        
        for result in test_results:
            status_class = 'success' if result['success'] else 'failure'
            status_text = '✅ 成功' if result['success'] else '❌ 失败'
            
            html_content += f"""
                <div class="version {status_class}">
                    <div class="version-header">
                        {result['version']} - 
                        <span class="{status_class}">{status_text}</span>
                        ({result['duration']}ms)
                    </div>
"""
            
            if result['error']:
                html_content += f'<div style="color: #dc3545;">错误: {result["error"]}</div>'
            
            if result['response']:
                response_json = json.dumps(result['response'], ensure_ascii=False, indent=2)
                html_content += f"""
                    <div class="response" onclick="this.style.maxHeight = this.style.maxHeight === 'none' ? '300px' : 'none'" title="点击展开/收起">
{response_json}</div>
"""
            
            html_content += "</div>"
        
        html_content += """
            </div>
        </div>
"""
    
    html_content += """
    </div>
</body>
</html>
"""
    
    # 写入文件
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return str(filename)


def main():
    parser = argparse.ArgumentParser(description='AI策略生成测试工具')
    parser.add_argument('--url', default=DEFAULT_CONFIG['base_url'], 
                       help='API基础URL')
    parser.add_argument('--auth', dest='auth_email', 
                       help='认证邮箱 (x-auth-user header)')
    parser.add_argument('--versions', default=','.join(DEFAULT_CONFIG['versions']),
                       help='测试版本，逗号分隔')
    parser.add_argument('--timeout', type=int, default=DEFAULT_CONFIG['timeout'],
                       help='请求超时时间（秒）')
    
    args = parser.parse_args()
    
    # 配置
    config = {
        'base_url': args.url,
        'versions': [v.strip() for v in args.versions.split(',')],
        'auth_email': args.auth_email,
        'timeout': args.timeout
    }
    
    print('🚀 开始AI策略生成测试')
    print(f'📋 测试用例: {len(TEST_CASES)}个')
    print(f'🔄 测试版本: {", ".join(config["versions"])}')
    print(f'🌐 API地址: {config["base_url"]}')
    print()
    
    results = []
    
    # 运行测试
    for test_case in TEST_CASES:
        for version in config['versions']:
            result = run_test(test_case, version, config)
            results.append(result)
            
            print(f"   {'✅' if result['success'] else '❌'} {result['duration']}ms")
            
            # 避免请求过快
            time.sleep(1)
        print()
    
    # 生成报告
    print('📊 测试完成！')
    success_count = sum(1 for r in results if r['success'])
    print(f'✅ 成功: {success_count}/{len(results)}')

    # 保存策略结果到JSON文件
    json_file = save_results_to_file(results, config)

    report_file = generate_html_report(results, config)
    print(f'📄 报告已生成: {report_file}')
    
    # 自动打开报告
    try:
        webbrowser.open(f'file://{Path(report_file).absolute()}')
        print('🌐 报告已在浏览器中打开')
    except Exception as e:
        print(f'⚠️  无法自动打开浏览器: {e}')
        print(f'请手动打开: {report_file}')


if __name__ == '__main__':
    main()
