import { Hono } from 'hono';
import {
  updatePortfolio,
  timeFly,
  getUserPortfolios,
  getPortfolioDetails,
  createCustomPortfolio,
  updateCustomPortfolio,
  deleteCustomPortfolio,
        getPublicPortfolioInfo,
        getOfficialPortfolios,
        getPortfolioSignalDatabase,
        getPortfolioSignalPresignedUrl,
        getPortfolioSignalDatabasePublic
} from '../services/portfolioService';

const router = new Hono();

router.post('/update_portfolio', updatePortfolio);
router.post('/time_fly', timeFly);
router.get('/user_portfolios', getUserPortfolios);
router.get('/official_portfolios', getOfficialPortfolios);

// Public routes (no authentication required, will be added to gateway whitelist)
router.get('/portfolios/public/download_signal_db/:symbol', getPortfolioSignalDatabasePublic);

// More specific routes first to avoid conflicts
router.get('/portfolios/signals/:signalCode', getPortfolioSignalDatabase);
router.get('/portfolios/signals_url/:urlCode', getPortfolioSignalPresignedUrl);
router.get('/portfolios/public/:publicCode', getPublicPortfolioInfo);

// General portfolio routes
router.get('/portfolios/:code', getPortfolioDetails);
router.post('/portfolios', createCustomPortfolio);
router.put('/portfolios/:updateCode', updateCustomPortfolio);
router.delete('/portfolios/:deleteCode', deleteCustomPortfolio);

export { router as portfolioRoutes };
