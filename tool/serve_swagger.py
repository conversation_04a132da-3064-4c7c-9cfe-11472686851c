#!/usr/bin/env python3
"""
Swagger UI Server for My Invest Strategy Portfolio API

This script serves a local Swagger UI interface for testing the API.
It loads the OpenAPI specification from doc/api/openapi.yaml and provides
an interactive interface for testing API endpoints.

Usage:
    python tool/serve_swagger.py
    python -m tool.serve_swagger (if run from project root)

Requirements:
    - flask
    - pyyaml
    - swagger-ui-py (optional, for better UI)

Author: <EMAIL>
"""

import os
import sys
import yaml
import requests
from pathlib import Path
from flask import Flask, render_template_string, jsonify, request, Response

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

app = Flask(__name__)

# Configuration
PORT = int(os.environ.get('PORT', 8888))
HOST = os.environ.get('HOST', 'localhost')
OPENAPI_SPEC_PATH = project_root / 'doc' / 'api' / 'openapi.yaml'
API_BASE_URL = 'https://api.myinvestpilot.com/strategy_portfolio'
# API_BASE_URL = 'http://localhost:8787/strategy_portfolio'

# Swagger UI HTML template
SWAGGER_UI_HTML = """
<!DOCTYPE html>
<html>
<head>
    <title>My Invest Strategy Portfolio API</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.10.3/swagger-ui.css" />
    <style>
        html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }
        *, *:before, *:after { box-sizing: inherit; }
        body { margin:0; background: #fafafa; }
        .swagger-ui .topbar { display: none; }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>

    <!-- JWT Setup Modal -->
    <div id="jwt-modal" style="display: none; position: fixed; z-index: 10000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
        <div style="background-color: white; margin: 10% auto; padding: 30px; border-radius: 8px; width: 500px; max-width: 90%; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2 style="margin: 0; color: #333;">🔐 JWT Authentication</h2>
                <button onclick="closeJwtModal()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #999;">&times;</button>
            </div>

            <div style="margin-bottom: 20px;">
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin-bottom: 15px;">
                    <strong>⚠️ Manual JWT Token Setup Required</strong>
                    <p style="margin: 8px 0 0 0; color: #856404;">You must manually extract and set the JWT token from your browser cookies. Automatic authentication is not supported.</p>
                </div>

                <p><strong>Step 1:</strong> Login to <a href="https://api.myinvestpilot.com" target="_blank" style="color: #007bff;">https://api.myinvestpilot.com</a> in a new tab</p>
                <p><strong>Step 2:</strong> Extract JWT token from browser cookies:</p>
                <ol style="margin: 10px 0; padding-left: 20px; color: #666;">
                    <li>Open browser Developer Tools (F12)</li>
                    <li>Go to <strong>Application</strong> tab → <strong>Storage</strong> → <strong>Cookies</strong></li>
                    <li>Find the <code style="background: #f5f5f5; padding: 2px 4px; border-radius: 3px;">jwt</code> cookie and copy its value</li>
                    <li>Paste the token in the field below</li>
                </ol>
            </div>

            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: bold;">JWT Token:</label>
                <textarea id="jwt-token" placeholder="Paste your JWT token here..."
                         style="width: 100%; height: 80px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-family: monospace; font-size: 12px; resize: vertical;"></textarea>
            </div>

            <div id="jwt-status" style="margin-bottom: 20px; padding: 10px; border-radius: 4px; font-weight: bold; display: none;"></div>

            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                <button onclick="clearJwtCookie()" style="padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Clear Cookie
                </button>
                <button onclick="setJwtCookie()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Set JWT Cookie
                </button>
            </div>
        </div>
    </div>
    <script src="https://unpkg.com/swagger-ui-dist@5.10.3/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@5.10.3/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: '/openapi.yaml',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                tryItOutEnabled: true,
                requestInterceptor: function(request) {
                    console.log('🚀 API Request:', request.method, request.url);
                    return request;
                },
                responseInterceptor: function(response) {
                    console.log('📥 API Response:', response.status, response.url);
                    return response;
                },
                onComplete: function() {
                    // Add custom JWT Auth button after Swagger UI loads
                    addJwtAuthButton();
                    // Check initial auth status
                    setTimeout(checkJwtCookieStatus, 1500);
                }
            });
        };

        function addJwtAuthButton() {
            // Wait for the auth section to be rendered
            setTimeout(function() {
                // Look for the auth section in Swagger UI
                const authWrapper = document.querySelector('.swagger-ui .auth-wrapper');
                const infoContainer = document.querySelector('.swagger-ui .info');

                if (infoContainer && !document.getElementById('jwt-auth-section')) {
                    // Create JWT auth section
                    const jwtSection = document.createElement('div');
                    jwtSection.id = 'jwt-auth-section';
                    jwtSection.style.cssText = `
                        background: #f7f7f7;
                        border: 1px solid #d3d3d3;
                        border-radius: 4px;
                        padding: 15px;
                        margin: 20px 0;
                        font-family: sans-serif;
                    `;

                    jwtSection.innerHTML = `
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <strong>🔐 JWT Authentication</strong>
                                <div style="font-size: 12px; color: #666; margin-top: 4px;">
                                    Manual JWT token setup required for API authentication
                                </div>
                            </div>
                            <button onclick="openJwtModal()" style="
                                background: #49cc90;
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 4px;
                                cursor: pointer;
                                font-size: 14px;
                                font-weight: bold;
                            ">Configure</button>
                        </div>
                    `;

                    // Insert after the info section
                    infoContainer.parentNode.insertBefore(jwtSection, infoContainer.nextSibling);
                }
            }, 1000);
        }

        // JWT Cookie Management Functions
        function setJwtCookie() {
            const token = document.getElementById('jwt-token').value.trim();
            if (!token) {
                updateJwtStatus('❌ Please enter a JWT token', 'red');
                return;
            }

            // Set the JWT cookie
            document.cookie = `jwt=${token}; path=/; SameSite=Lax`;
            updateJwtStatus('✅ JWT cookie set successfully! You can now test the API.', 'green');
            updateMainAuthStatus('✅ Authenticated', 'green');

            // Clear the input for security
            document.getElementById('jwt-token').value = '';
        }

        function clearJwtCookie() {
            // Clear the JWT cookie
            document.cookie = 'jwt=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
            updateJwtStatus('🗑️ JWT cookie cleared', 'orange');
            updateMainAuthStatus('⚠️ Not authenticated', 'orange');
            document.getElementById('jwt-token').value = '';
        }

        function updateJwtStatus(message, color) {
            const statusDiv = document.getElementById('jwt-status');
            statusDiv.textContent = message;
            statusDiv.style.color = color;
            statusDiv.style.display = 'block';

            if (color === 'green') {
                statusDiv.style.background = '#d4edda';
                statusDiv.style.borderColor = '#c3e6cb';
            } else if (color === 'red') {
                statusDiv.style.background = '#f8d7da';
                statusDiv.style.borderColor = '#f5c6cb';
            } else {
                statusDiv.style.background = '#fff3cd';
                statusDiv.style.borderColor = '#ffeaa7';
            }
        }

        function openJwtModal() {
            document.getElementById('jwt-modal').style.display = 'block';
            // Check current cookie status
            checkJwtCookieStatus();
        }

        function closeJwtModal() {
            document.getElementById('jwt-modal').style.display = 'none';
            document.getElementById('jwt-token').value = '';
            document.getElementById('jwt-status').style.display = 'none';
        }

        function checkJwtCookieStatus() {
            const cookies = document.cookie.split(';');
            const jwtCookie = cookies.find(cookie => cookie.trim().startsWith('jwt='));

            // Update both modal and main section
            if (jwtCookie) {
                updateJwtStatus('✅ JWT cookie is currently set and ready for API testing.', 'green');
                updateMainAuthStatus('✅ Authenticated', 'green');
            } else {
                updateJwtStatus('⚠️ No JWT cookie found. Please set your JWT token below.', 'orange');
                updateMainAuthStatus('⚠️ Not authenticated', 'orange');
            }
        }

        function updateMainAuthStatus(message, color) {
            const authSection = document.getElementById('jwt-auth-section');
            if (authSection) {
                const statusDiv = authSection.querySelector('.auth-status') || document.createElement('div');
                statusDiv.className = 'auth-status';
                statusDiv.textContent = message;
                statusDiv.style.cssText = `
                    font-size: 12px;
                    font-weight: bold;
                    color: ${color};
                    margin-top: 4px;
                `;

                if (!authSection.querySelector('.auth-status')) {
                    authSection.querySelector('div').appendChild(statusDiv);
                }
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('jwt-modal');
            if (event.target === modal) {
                closeJwtModal();
            }
        }
    </script>
</body>
</html>
"""

def load_openapi_spec():
    """Load and validate OpenAPI specification."""
    if not OPENAPI_SPEC_PATH.exists():
        raise FileNotFoundError(f"OpenAPI specification not found at: {OPENAPI_SPEC_PATH}")
    
    try:
        with open(OPENAPI_SPEC_PATH, 'r', encoding='utf-8') as f:
            spec = yaml.safe_load(f)
        return spec
    except yaml.YAMLError as e:
        raise ValueError(f"Invalid YAML in OpenAPI specification: {e}")
    except Exception as e:
        raise RuntimeError(f"Error loading OpenAPI specification: {e}")

@app.route('/')
def swagger_ui():
    """Serve the Swagger UI interface."""
    return render_template_string(SWAGGER_UI_HTML)

@app.route('/openapi.yaml')
def openapi_spec():
    """Serve the OpenAPI specification file."""
    try:
        spec = load_openapi_spec()
        # Convert to JSON for better browser compatibility
        return jsonify(spec)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/openapi.json')
def openapi_spec_json():
    """Serve the OpenAPI specification as JSON."""
    return openapi_spec()

@app.route('/health')
def health_check():
    """Health check endpoint."""
    try:
        spec = load_openapi_spec()
        return jsonify({
            "status": "healthy",
            "swagger_ui_url": f"http://{HOST}:{PORT}",
            "openapi_spec_url": f"http://{HOST}:{PORT}/openapi.yaml",
            "spec_file_path": str(OPENAPI_SPEC_PATH),
            "api_title": spec.get('info', {}).get('title', 'Unknown'),
            "api_version": spec.get('info', {}).get('version', 'Unknown'),
            "servers": spec.get('servers', []),
            "timestamp": "2024-01-01T00:00:00Z"  # You can use datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            "status": "unhealthy",
            "error": str(e)
        }), 500

@app.route('/api/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])
def proxy_api(path):
    """Proxy API requests to avoid CORS issues."""
    try:
        # Build the target URL
        target_url = f"{API_BASE_URL}/{path}"

        # Get query parameters
        query_params = request.args.to_dict()

        # Get request headers (exclude host and other problematic headers)
        headers = {}
        for key, value in request.headers:
            if key.lower() not in ['host', 'content-length', 'connection']:
                headers[key] = value

        # Force no compression to avoid zstd issues
        headers['Accept-Encoding'] = 'gzip, deflate'

        # Get request data
        data = None
        if request.method in ['POST', 'PUT', 'PATCH']:
            if request.is_json:
                data = request.get_json()
            else:
                data = request.get_data()

        # Make the request
        response = requests.request(
            method=request.method,
            url=target_url,
            params=query_params,
            headers=headers,
            json=data if request.is_json else None,
            data=data if not request.is_json else None,
            timeout=30
        )

        # Handle response content properly (decode if compressed)
        try:
            # Try to get JSON content first (requests automatically handles decompression)
            if response.headers.get('content-type', '').startswith('application/json'):
                response_data = response.json()

                # Create JSON response - don't add CORS headers since production API already has them
                flask_response = jsonify(response_data)
                flask_response.status_code = response.status_code

                return flask_response
            else:
                # For non-JSON responses, return binary content (important for file downloads)
                excluded_headers = ['content-encoding', 'content-length', 'transfer-encoding', 'connection']
                response_headers = [(name, value) for (name, value) in response.headers.items()
                              if name.lower() not in excluded_headers]

                return Response(
                    response.content,  # Use .content for binary data, not .text
                    status=response.status_code,
                    headers=response_headers
                )
        except ValueError:
            # If JSON parsing fails, return binary content (important for file downloads)
            excluded_headers = ['content-encoding', 'content-length', 'transfer-encoding', 'connection']
            response_headers = [(name, value) for (name, value) in response.headers.items()
                          if name.lower() not in excluded_headers]

            return Response(
                response.content,  # Use .content for binary data, not .text
                status=response.status_code,
                headers=response_headers
            )

    except requests.exceptions.RequestException as e:
        print(f"❌ Proxy error: {e}")
        return jsonify({"error": f"Proxy request failed: {str(e)}"}), 500
    except Exception as e:
        print(f"❌ Unexpected proxy error: {e}")
        return jsonify({"error": f"Unexpected error: {str(e)}"}), 500

@app.route('/api/<path:path>', methods=['OPTIONS'])
def proxy_api_options(path):
    """Handle preflight OPTIONS requests for CORS."""
    response = Response()
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, PATCH, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
    response.headers['Access-Control-Allow-Credentials'] = 'true'
    return response

def print_startup_info():
    """Print startup information."""
    print("=" * 80)
    print("🚀 Swagger UI Server Started Successfully!")
    print("=" * 80)
    print(f"📖 Swagger UI:           http://{HOST}:{PORT}")
    print(f"📄 OpenAPI Spec:         http://{HOST}:{PORT}/openapi.yaml")
    print(f"🔍 Health Check:         http://{HOST}:{PORT}/health")
    print(f"📁 Spec File:            {OPENAPI_SPEC_PATH}")
    print()
    print("📋 Testing Instructions:")
    print("1. Login to https://api.myinvestpilot.com in your browser")
    print("2. Open the Swagger UI URL in the SAME browser")
    print("3. Click 'Configure' in JWT Authentication section")
    print("4. Manually extract and set JWT token from browser cookies")
    print("5. Test API endpoints directly!")
    print()
    print("⚠️  Important Notes:")
    print("- API calls will be sent to the server configured in openapi.yaml")
    print("- Make sure your target API server is accessible and running")
    print("- Use valid test data to avoid affecting production data")
    print()
    print("🛑 To stop the server: Press Ctrl+C")
    print("=" * 80)

def main():
    """Main entry point."""
    try:
        # Validate OpenAPI spec on startup
        spec = load_openapi_spec()
        print(f"✅ Loaded OpenAPI spec: {spec.get('info', {}).get('title', 'Unknown')}")
        
        # Print startup information
        print_startup_info()
        
        # Start the server
        app.run(
            host=HOST,
            port=PORT,
            debug=False,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n📴 Shutting down Swagger UI server...")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
