# API Authentication Guide

The My Invest Strategy Portfolio API uses gateway-based authentication for secure access.

## 🔐 Gateway Authentication (Automatic)

### How it Works
Authentication is handled by the API gateway using JWT tokens. When you login to the main application, the gateway manages authentication automatically.

### For Swagger UI Testing

**Simple 3-Step Process:**
1. Login to `https://api.myinvestpilot.com` in your browser
2. Open Swagger UI in the same browser: `http://localhost:8888`
3. Test API endpoints - authentication is handled automatically by the gateway

**No manual configuration needed!** The gateway handles all authentication details.

### Cookie Details
- **Cookie Name**: `jwt`
- **Domain**: `api.myinvestpilot.com`
- **Automatic Inclusion**: Yes, when testing from Swagger UI
- **Expiration**: Based on your login session

### Example Cookie Value
```
jwt=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************.bTgwYHcD9vz6cros4IiAb9CoAZY-SZIUe3enGXEq-2A
```

## 📧 Method 2: Header Authentication (Alternative)

### How it Works
Some endpoints may also support email-based header authentication as a fallback method.

### Usage in Swagger UI
1. Click the "Authorize" button in Swagger UI
2. In the "headerAuth" section, enter your email address
3. Click "Authorize"

### Header Details
- **Header Name**: `x-auth-user`
- **Header Value**: Your email address (e.g., `<EMAIL>`)
- **Usage**: Manual configuration required

## 🔓 Public Endpoints (No Authentication Required)

These endpoints can be accessed without any authentication:

- `GET /search_symbols` - Search for symbols
- `GET /official_portfolios` - Get official portfolios
- `GET /portfolios/public/{code}` - Get public portfolio details

## 🛠️ For API Clients (curl, Postman, etc.)

### Using Cookie Authentication
```bash
curl --location 'https://api.myinvestpilot.com/strategy_portfolio/user_portfolios' \
--header 'Cookie: jwt=YOUR_JWT_TOKEN_HERE'
```

### Using Header Authentication
```bash
curl --location 'https://api.myinvestpilot.com/strategy_portfolio/user_portfolios' \
--header 'x-auth-user: <EMAIL>'
```

## 🔍 Troubleshooting

### Cookie Not Working?
1. **Check Login Status**: Ensure you're logged in to `https://api.myinvestpilot.com`
2. **Same Browser**: Use the same browser for login and Swagger UI
3. **Clear Cache**: Try clearing browser cache and cookies, then login again
4. **Check Domain**: Ensure the cookie domain matches the API domain

### Getting 401/403 Errors?
1. **Verify Authentication**: Check if you're properly authenticated
2. **Check Cookie Expiration**: Your login session may have expired
3. **Try Re-login**: Logout and login again to refresh the JWT token
4. **Use Header Auth**: Try the alternative header authentication method

### CORS Issues?
1. **Same Origin**: The Swagger UI server is configured to handle CORS
2. **Credentials**: Cookies are automatically included with `credentials: include`
3. **Browser Console**: Check for CORS errors in browser developer tools

## 🎯 Best Practices

### For Development Testing
1. **Use Cookie Auth**: Easier and more realistic for browser-based testing
2. **Keep Login Active**: Don't logout from the main application while testing
3. **Test Both Methods**: Verify both cookie and header authentication work

### For Production Integration
1. **Implement JWT**: Use the JWT cookie method for web applications
2. **Handle Expiration**: Implement proper token refresh logic
3. **Secure Storage**: Store JWT tokens securely (httpOnly cookies recommended)

### Security Notes
1. **HTTPS Only**: Always use HTTPS in production
2. **Token Expiration**: JWT tokens have expiration times
3. **Secure Cookies**: Production cookies should be httpOnly and secure
4. **Domain Restrictions**: Cookies are domain-specific for security

## 📋 Testing Checklist

- [ ] Login to main application in browser
- [ ] Open Swagger UI in same browser
- [ ] Test public endpoints (should work without auth)
- [ ] Test protected endpoints with cookie auth
- [ ] Verify JWT cookie is included in requests
- [ ] Test header authentication as fallback
- [ ] Check error handling for invalid/expired tokens
