import { Pool, PoolClient, PoolConfig } from 'pg';
import { DBContext } from '../types';

const DB_CONFIG: PoolConfig = {
  connectionTimeoutMillis: 10000,
  statement_timeout: 30000,
  query_timeout: 30000,
  max: 1,
  idleTimeoutMillis: 0
};

let poolCount = 0;

function createPool(dbUrl: string): Pool {
  poolCount++;
  console.log(`[DB] Creating new pool #${poolCount}`);
  return new Pool({
    ...DB_CONFIG,
    connectionString: dbUrl,
  });
}

// 记录查询执行时间的工具函数
async function timeQuery<T>(name: string, fn: () => Promise<T>): Promise<T> {
  const start = Date.now();
  try {
    const result = await fn();
    const duration = Date.now() - start;
    console.log(`[DB] ${name} completed in ${duration}ms`);
    return result;
  } catch (error) {
    const duration = Date.now() - start;
    console.error(`[DB] ${name} failed after ${duration}ms:`, error);
    throw error;
  }
}

async function basicQuery(c: DBContext, text: string, params: any[]) {
  const pool = createPool(c.env.DB_URL);
  const queryId = Math.random().toString(36).substring(7);

  console.log(`[DB] Query ${queryId} started:`, {
    text,
    paramCount: params.length
  });

  try {
    const result = await timeQuery(`Query ${queryId}`, () => pool.query(text, params));
    console.log(`[DB] Query ${queryId} success:`, {
      rowCount: result.rowCount,
      commandTag: result.command
    });
    return result;
  } catch (error) {
    console.error(`[DB] Query ${queryId} error:`, {
      error,
      text,
      params
    });
    throw error;
  } finally {
    try {
      await pool.end();
      console.log(`[DB] Connection pool ${queryId} ended`);
    } catch (error) {
      console.error(`[DB] Error ending pool ${queryId}:`, error);
    }
  }
}

async function queryWithRetry(c: DBContext, text: string, params: any[], retries = 3) {
  const operationId = Math.random().toString(36).substring(7);
  console.log(`[DB] Starting operation ${operationId} with ${retries} retries`);

  for (let attempt = 0; attempt < retries; attempt++) {
    try {
      const result = await basicQuery(c, text, params);
      if (attempt > 0) {
        console.log(`[DB] Operation ${operationId} succeeded after ${attempt + 1} attempts`);
      }
      return result;
    } catch (error) {
      const isLastAttempt = attempt === retries - 1;
      if (isLastAttempt) {
        console.error(`[DB] Operation ${operationId} failed after all ${retries} attempts:`, error);
        throw error;
      }
      const delay = Math.pow(2, attempt) * 100;
      console.warn(`[DB] Operation ${operationId} attempt ${attempt + 1} failed, retrying in ${delay}ms:`, error);
      await new Promise(r => setTimeout(r, delay));
    }
  }
  throw new Error('Unreachable');
}

async function withTransaction<T>(
  c: DBContext,
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  const pool = createPool(c.env.DB_URL);
  const transactionId = Math.random().toString(36).substring(7);
  console.log(`[DB] Starting transaction ${transactionId}`);

  const client = await timeQuery(
    `Transaction ${transactionId} connection`,
    () => pool.connect()
  );

  try {
    await timeQuery(
      `Transaction ${transactionId} BEGIN`,
      () => client.query('BEGIN')
    );

    const result = await timeQuery(
      `Transaction ${transactionId} execution`,
      () => callback(client)
    );

    await timeQuery(
      `Transaction ${transactionId} COMMIT`,
      () => client.query('COMMIT')
    );

    console.log(`[DB] Transaction ${transactionId} completed successfully`);
    return result;
  } catch (error) {
		const errorDetail = error instanceof Error ? {
			message: error.message,
			stack: error.stack,
			name: error.name
		} : error;

    console.error(`[DB] Transaction ${transactionId} failed:`, errorDetail);
    try {
      await timeQuery(
        `Transaction ${transactionId} ROLLBACK`,
        () => client.query('ROLLBACK')
      );
      console.log(`[DB] Transaction ${transactionId} rolled back`);
    } catch (rollbackError) {
      console.error(`[DB] Transaction ${transactionId} rollback failed:`, rollbackError);
    }
    throw error;
  } finally {
    client.release();
    try {
      await pool.end();
      console.log(`[DB] Transaction ${transactionId} pool ended`);
    } catch (error) {
      console.error(`[DB] Error ending transaction ${transactionId} pool:`, error);
    }
  }
}

export {
  withTransaction,
  queryWithRetry as query
};
