/**
 * Temporary download token configuration constants
 */

// Token expiry time
export const TEMP_TOKEN_EXPIRY_MS = 30 * 60 * 1000; // 30 minutes in milliseconds
export const TEMP_TOKEN_EXPIRY_SECONDS = 30 * 60; // 30 minutes in seconds

// Download limits
export const TEMP_TOKEN_MAX_DOWNLOADS = 3; // Maximum downloads per token

// Redis key prefix
export const TEMP_TOKEN_PREFIX = 'temp_download:';

// API base path (must match the basePath in app.ts)
export const API_BASE_PATH = '/strategy_portfolio';

/**
 * Interface for temporary token payload stored in Redis
 */
export interface TemporaryTokenPayload {
  code: string;
  email: string;
  expiry: number;
  downloads: number;
  maxDownloads: number;
}

/**
 * Validates the structure of temporary token data
 */
export function validateTokenData(data: any): data is TemporaryTokenPayload {
  return (
    typeof data === 'object' &&
    data !== null &&
    typeof data.code === 'string' &&
    typeof data.email === 'string' &&
    typeof data.expiry === 'number' &&
    typeof data.downloads === 'number' &&
    typeof data.maxDownloads === 'number'
  );
}
