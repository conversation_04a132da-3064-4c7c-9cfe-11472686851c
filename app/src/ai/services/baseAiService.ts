/**
 * 基础AI服务类
 * 提供通用的AI调用功能
 */

import { Context } from 'hono';
import { BaseAiRequest, BaseAiResponse, AiServiceType } from '../types';
import { getSystemPrompt, isValidVersion, getDefaultVersion } from '../promptManager';
import { Env } from '../../types';

/**
 * Schema管理器 - 从R2动态获取外部schema
 */
class SchemaManager {
  private static instance: SchemaManager;

  static getInstance(): SchemaManager {
    if (!SchemaManager.instance) {
      SchemaManager.instance = new SchemaManager();
    }
    return SchemaManager.instance;
  }

  /**
   * 从R2获取完整的schema，用于AI生成
   * 返回包含所有definitions的完整schema，以StrategyDefinitionForAI为根
   */
  async getStrategyDefinitionSchema(env: Env): Promise<any> {
    try {
      console.log('Loading schema from R2: myinvestpilot/primitives_schema.json');

      const object = await env.BUCKET.get('myinvestpilot/primitives_schema.json');

      if (!object) {
        console.warn('Schema file not found in R2, using fallback');
        return this.getFallbackCompleteSchema();
      }

      const fullSchema = await object.json() as any;

      // 检查是否包含必要的定义
      if (fullSchema?.definitions?.StrategyDefinitionForAI) {
        console.log('Successfully loaded complete schema from R2');

        // 返回完整的schema，但以StrategyDefinitionForAI为根
        return {
          ...fullSchema.definitions.StrategyDefinitionForAI,
          definitions: fullSchema.definitions  // 包含所有引用的定义
        };
      } else {
        console.warn('StrategyDefinitionForAI not found in schema, using fallback');
        return this.getFallbackCompleteSchema();
      }

    } catch (error) {
      console.error('Failed to load schema from R2:', error);
      return this.getFallbackCompleteSchema();
    }
  }

  /**
   * 获取完整的外部schema（用于其他用途）
   */
  async getFullExternalSchema(env: Env): Promise<any> {
    try {
      const object = await env.BUCKET.get('myinvestpilot/primitives_schema.json');

      if (!object) {
        console.warn('Schema file not found in R2');
        return null;
      }

      return await object.json();
    } catch (error) {
      console.error('Failed to load full schema from R2:', error);
      return null;
    }
  }

  /**
   * Fallback完整schema - 当R2不可用时使用
   */
  private getFallbackCompleteSchema(): any {
    return {
      type: "object",
      properties: {
        market_indicators: {
          type: "object",
          properties: {
            indicators: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  code: { type: "string" }
                },
                required: ["code"],
                additionalProperties: true
              }
            },
            transformers: {
              type: "array",
              items: { type: "object", additionalProperties: true }
            }
          },
          required: ["indicators"],
          additionalProperties: true
        },
        trade_strategy: {
          type: "object",
          properties: {
            indicators: {
              type: "array",
              items: { type: "object", additionalProperties: true }
            },
            signals: {
              type: "array",
              items: { type: "object", additionalProperties: true }
            },
            outputs: {
              type: "object",
              properties: {
                buy_signal: { type: "string" },
                sell_signal: { type: "string" }
              },
              required: ["buy_signal", "sell_signal"],
              additionalProperties: true
            }
          },
          required: ["signals", "outputs"],
          additionalProperties: true
        }
      },
      required: ["trade_strategy"],
      additionalProperties: true,
      // 包含基本的definitions以支持引用
      definitions: {
        MarketIndicatorsBody: {
          type: "object",
          properties: {
            indicators: { type: "array", items: { type: "object", additionalProperties: true } },
            transformers: { type: "array", items: { type: "object", additionalProperties: true } }
          },
          required: ["indicators"],
          additionalProperties: true
        },
        TradeStrategyBody: {
          type: "object",
          properties: {
            indicators: { type: "array", items: { type: "object", additionalProperties: true } },
            signals: { type: "array", items: { type: "object", additionalProperties: true } },
            outputs: { type: "object", additionalProperties: true }
          },
          required: ["signals", "outputs"],
          additionalProperties: true
        }
      }
    };
  }
}

// 导出schema管理器实例
export const schemaManager = SchemaManager.getInstance();

/**
 * 自定义AI服务错误类型
 */
export class AiServiceError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly isClientSafe: boolean = false
  ) {
    super(message);
    this.name = 'AiServiceError';
  }
}

/**
 * 预定义的错误类型
 */
export const AI_ERROR_CODES = {
  API_KEY_NOT_CONFIGURED: 'API_KEY_NOT_CONFIGURED',
  AI_MODEL_REQUEST_FAILED: 'AI_MODEL_REQUEST_FAILED',
  INVALID_RESPONSE_FORMAT: 'INVALID_RESPONSE_FORMAT',
  PROMPT_LOADING_FAILED: 'PROMPT_LOADING_FAILED',
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
} as const;

// 所有硬编码的schema已移除，现在使用从R2动态获取的schema

export abstract class BaseAiService<TRequest extends BaseAiRequest, TResponse extends BaseAiResponse> {
  protected serviceType: AiServiceType;

  constructor(serviceType: AiServiceType) {
    this.serviceType = serviceType;
  }

  /**
   * 处理AI请求的主要方法
   */
  async processRequest(c: Context, requestBody: TRequest): Promise<Response> {
    try {
      // 验证请求
      const validationError = this.validateRequest(requestBody);
      if (validationError) {
        return c.json({
          success: false,
          error: validationError
        } as TResponse, 400);
      }

      // 确定使用的prompt版本
      const promptVersion = this.determinePromptVersion(requestBody);

      // 获取System Prompt
      let systemPrompt: string;
      try {
        systemPrompt = await getSystemPrompt(this.serviceType, promptVersion);
      } catch (error) {
        console.error('Failed to load system prompt:', error);
        return c.json({
          success: false,
          error: 'System configuration error. Please check server logs for details.'
        } as TResponse, 500);
      }

      // 调用AI模型
      const aiResponse = await this.callAiModel(c, systemPrompt, requestBody);

      // 处理AI响应
      const processedResponse = await this.processAiResponse(aiResponse, promptVersion);

      return c.json(processedResponse);
    } catch (error) {
      return this.handleError(c, error);
    }
  }

  /**
   * 统一错误处理
   */
  private handleError(c: Context, error: unknown): Response {
    // 记录详细错误信息到服务器日志
    console.error(`Error in ${this.serviceType} service:`, error);

    let clientMessage = 'Internal server error. Please check server logs for details.';
    let statusCode = 500;

    // 处理自定义AI服务错误
    if (error instanceof AiServiceError) {
      console.error(`AI Service Error [${error.code}]:`, error.message);

      if (error.isClientSafe) {
        clientMessage = error.message;
      } else {
        // 为开发者提供更多上下文，但不暴露给客户端
        console.error(`Error details for debugging: ${error.message}`);

        // 根据错误代码提供稍微更具体的信息
        switch (error.code) {
          case AI_ERROR_CODES.API_KEY_NOT_CONFIGURED:
            clientMessage = 'AI service configuration error. Please contact support.';
            break;
          case AI_ERROR_CODES.AI_MODEL_REQUEST_FAILED:
            clientMessage = 'AI model temporarily unavailable. Please try again later.';
            statusCode = 503; // Service Unavailable
            break;
          case AI_ERROR_CODES.PROMPT_LOADING_FAILED:
            clientMessage = 'Service configuration error. Please contact support.';
            break;
          case AI_ERROR_CODES.VALIDATION_FAILED:
            clientMessage = 'Request validation failed. Please check your input.';
            statusCode = 400; // Bad Request
            break;
          default:
            // 保持默认的通用错误消息
            break;
        }
      }
    } else if (error instanceof Error) {
      // 标准Error对象 - 记录但不暴露详细信息
      console.error(`Standard Error: ${error.message}`);
      console.error(`Stack trace:`, error.stack);
    } else {
      // 未知错误类型
      console.error(`Unknown error type:`, error);
    }

    return c.json({
      success: false,
      error: clientMessage
    } as TResponse, statusCode);
  }

  /**
   * 确定使用的prompt版本
   */
  protected determinePromptVersion(requestBody: TRequest): string {
    let promptVersion = getDefaultVersion(this.serviceType);

    if (requestBody.promptVersion) {
      if (isValidVersion(this.serviceType, requestBody.promptVersion)) {
        promptVersion = requestBody.promptVersion;
      } else {
        console.warn(`Invalid prompt version requested: ${requestBody.promptVersion}, using default: ${promptVersion}`);
      }
    }

    return promptVersion;
  }

  /**
   * 调用AI模型
   */
  protected async callAiModel(c: Context, systemPrompt: string, requestBody: TRequest): Promise<any> {
    const env = c.env as Env;

    if (!env.OPENAI_API_KEY) {
      throw new AiServiceError(
        'OpenAI API key not configured',
        AI_ERROR_CODES.API_KEY_NOT_CONFIGURED
      );
    }

    // 获取动态schema
    const dynamicSchema = await schemaManager.getStrategyDefinitionSchema(env);

    // 确定API端点：优先使用AI Gateway，否则直连OpenAI
    const apiUrl = env.AI_GATEWAY_URL || 'https://api.openai.com/v1/chat/completions';

    console.log(`Using AI API endpoint: ${apiUrl.replace(env.OPENAI_API_KEY || '', '[REDACTED]')}`);

		const user = c.req.header('x-auth-user') || 'unknown';

    const openaiResponse = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
				"cf-aig-metadata": JSON.stringify({
						 client: "myInvestStrategyPortfolio",
             user,
           }),
      },
      body: JSON.stringify({
        model: requestBody.model || 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: requestBody.userInput
          }
        ],
        temperature: 0.1,
        max_tokens: 4000,
        response_format: {
          type: "json_schema",
          json_schema: {
            name: "strategy_definition",
            description: "Complete strategy definition including market indicators and trade strategy",
            schema: dynamicSchema
          }
        }
      })
    });

    if (!openaiResponse.ok) {
      const errorText = await openaiResponse.text();
      console.error('OpenAI API error:', openaiResponse.status, errorText);
      throw new AiServiceError(
        `AI model request failed: ${openaiResponse.status} ${errorText}`,
        AI_ERROR_CODES.AI_MODEL_REQUEST_FAILED
      );
    }

    return await openaiResponse.json();
  }

  /**
   * 抽象方法：验证请求
   */
  protected abstract validateRequest(requestBody: TRequest): string | null;

  /**
   * 抽象方法：处理AI响应
   */
  protected abstract processAiResponse(aiResponse: any, promptVersion: string): Promise<TResponse>;
}
