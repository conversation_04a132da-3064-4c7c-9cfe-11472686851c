import { Env, QueueMessage, ExecutionContext, PortfolioUpdateStatus, DBContext } from '../types';
import { query, withTransaction } from '../services/databaseService';
import { hasSubscribers, isPortfolioUpdated } from '../utils/helpers';
import { getPortfoliosForUpdate, updatePortfolioStatus } from '../utils/portfolioDatabaseHelpers';
import { constructPortfolioUpdateMessage } from '../utils/messageConstructor';
import { sendToMQ } from '../services/MQService';

export async function producerProcess(c: DBContext) {
  const batchSize = 100;
  let processed = 0;
  let hasMore = true;

  while (hasMore) {
    const portfolios = await getPortfoliosForUpdate(c, batchSize, processed);

    for (const portfolio of portfolios) {
			console.log(`Processing portfolio: code=${portfolio.code}, is_official=${portfolio.is_official}`);

      const hasActiveSubscribers = await hasSubscribers(c.env, portfolio.code);

			console.log(`Portfolio ${portfolio.code} has active subscribers: ${hasActiveSubscribers}`);

      if (portfolio.is_official || hasActiveSubscribers) {
        let shouldUpdate = true;

				console.log(`Portfolio ${portfolio.code} update status: ${portfolio.update_status}`);

        if (portfolio.update_status === PortfolioUpdateStatus.PENDING) {
          const isUpdated = await isPortfolioUpdated(c.env, portfolio.code);

					console.log(`Portfolio ${portfolio.code} is updated: ${isUpdated}`);

          if (isUpdated) {
            await updatePortfolioStatus(c, portfolio.code, PortfolioUpdateStatus.COMPLETED);
            console.log(`Portfolio ${portfolio.code} update completed`);
            shouldUpdate = false;
          } else {
            const timeSinceLastAttempt = portfolio.last_update_attempt
              ? Date.now() - new Date(portfolio.last_update_attempt).getTime()
              : Infinity;

            if (timeSinceLastAttempt <= 5 * 60 * 1000) { // 5 minutes in milliseconds
              console.log(`Skipping update for portfolio ${portfolio.code} as it's still being processed`);
              shouldUpdate = false;
            }
            // If more than 5 minutes have passed, we'll proceed with a new update attempt
          }
        }

        if (shouldUpdate) {
					console.log(`Sending update message for portfolio ${portfolio.code}`);

          const queueMessage = {
            portfolio_code: portfolio.code
          };
          await c.env.QUEUE.send(queueMessage);
          await updatePortfolioStatus(c, portfolio.code, PortfolioUpdateStatus.PENDING);
          console.log(`Sent update message for portfolio ${portfolio.code}`);
        }
      } else {
        console.log(`Skipping update for portfolio ${portfolio.code} as it's not official and has no active subscribers`);
      }
    }

    processed += portfolios.length;
    hasMore = portfolios.length === batchSize;
  }

  console.log(`Processed ${processed} portfolios for update`);
}

export async function consumerProcess(batch: MessageBatch<QueueMessage>, env: Env, ctx: ExecutionContext) {
  for (const message of batch.messages) {
    const { portfolio_code } = message.body;

    try {
      const updateMessage = await constructPortfolioUpdateMessage({env, executionCtx: ctx}, portfolio_code);
      sendToMQ(env, ctx, updateMessage);
      console.log(`Sent update message for portfolio ${portfolio_code}`);
      message.ack();
    } catch (error) {
      console.error(`Error processing portfolio ${portfolio_code}:`, error);
      await updatePortfolioStatus({env, executionCtx: ctx}, portfolio_code, PortfolioUpdateStatus.FAILED);
    }
  }
}

export async function cleanupUnsubscribedPortfolios(c: DBContext) {
  const BATCH_SIZE = 100;

  // 获取需要检查的组合列表
  const portfoliosResult = await query(c,
    'SELECT code, id FROM strategy.portfolios WHERE is_deleted = false AND is_official = false LIMIT $1',
    [BATCH_SIZE]
  );

  // 处理每个组合
  for (const portfolio of portfoliosResult.rows) {
    try {
      const hasActiveSubscribers = await hasSubscribers(c.env, portfolio.code);

      console.log(`Portfolio ${portfolio.code} has active subscribers: ${hasActiveSubscribers}`);

      if (!hasActiveSubscribers) {
        // 为每个需要更新的组合创建单独的事务
        await withTransaction(c, async (client) => {
          // Update portfolios table
          await client.query(
            'UPDATE strategy.portfolios SET is_deleted = true, updated_at = CURRENT_TIMESTAMP WHERE code = $1',
            [portfolio.code]
          );

          // Update user_portfolios table
          await client.query(
            `UPDATE strategy.user_portfolios
            SET is_deleted = true, updated_at = CURRENT_TIMESTAMP
            WHERE portfolio_id = $1 AND is_deleted = false`,
            [portfolio.id]
          );

          console.log(`Marked portfolio ${portfolio.code} and associated user_portfolios as deleted due to no subscribers`);
        });
      }
    } catch (error) {
      console.error(`Error processing portfolio ${portfolio.code}:`, error);
      continue;
    }
  }
}
