/**
 * Schema验证服务
 * 使用动态schema验证API输入
 */

import { Env } from '../types';
import { schemaManager } from '../ai/services/baseAiService';

/**
 * 验证结果接口
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * 简单的JSON Schema验证器
 * 注意：这是一个基础实现，主要验证结构和必需字段
 */
class SimpleSchemaValidator {
  /**
   * 验证对象是否符合schema
   */
  validate(data: any, schema: any): ValidationResult {
    const errors: string[] = [];

    try {
      this.validateObject(data, schema, '', errors);
    } catch (error) {
      errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  private validateObject(data: any, schema: any, path: string, errors: string[]): void {
    if (schema.type !== 'object') {
      return;
    }

    // 检查必需字段
    if (schema.required && Array.isArray(schema.required)) {
      for (const requiredField of schema.required) {
        if (!(requiredField in data)) {
          errors.push(`Missing required field: ${path}${requiredField}`);
        }
      }
    }

    // 验证属性
    if (schema.properties) {
      for (const [key, value] of Object.entries(data)) {
        const fieldSchema = schema.properties[key];
        if (fieldSchema) {
          this.validateField(value, fieldSchema, `${path}${key}.`, errors);
        } else if (!schema.additionalProperties) {
          errors.push(`Unexpected field: ${path}${key}`);
        }
      }
    }
  }

  private validateField(data: any, schema: any, path: string, errors: string[]): void {
    // 类型验证
    if (schema.type) {
      if (!this.checkType(data, schema.type)) {
        errors.push(`Invalid type for ${path}: expected ${schema.type}, got ${typeof data}`);
        return;
      }
    }

    // 递归验证对象
    if (schema.type === 'object') {
      this.validateObject(data, schema, path, errors);
    }

    // 验证数组
    if (schema.type === 'array' && Array.isArray(data)) {
      if (schema.items) {
        data.forEach((item, index) => {
          this.validateField(item, schema.items, `${path}[${index}].`, errors);
        });
      }
    }
  }

  private checkType(data: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof data === 'string';
      case 'number':
        return typeof data === 'number';
      case 'boolean':
        return typeof data === 'boolean';
      case 'object':
        return typeof data === 'object' && data !== null && !Array.isArray(data);
      case 'array':
        return Array.isArray(data);
      default:
        return true; // 未知类型，跳过验证
    }
  }
}

const validator = new SimpleSchemaValidator();

/**
 * 验证策略定义是否符合动态schema
 */
export async function validateStrategyDefinition(
  strategyDefinition: any,
  env: Env
): Promise<ValidationResult> {
  try {
    // 获取完整的动态schema（包含所有definitions）
    const completeSchema = await schemaManager.getStrategyDefinitionSchema(env);

    // 执行验证
    return validator.validate(strategyDefinition, completeSchema);
  } catch (error) {
    console.error('Schema validation error:', error);
    return {
      isValid: false,
      errors: [`Schema validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`]
    };
  }
}

/**
 * 验证交易策略部分
 */
export async function validateTradeStrategy(
  tradeStrategy: any,
  env: Env
): Promise<ValidationResult> {
  try {
    // 获取完整schema并提取trade_strategy部分
    const fullSchema = await schemaManager.getStrategyDefinitionSchema(env);
    const tradeStrategySchema = fullSchema?.properties?.trade_strategy;

    if (!tradeStrategySchema) {
      return {
        isValid: false,
        errors: ['Trade strategy schema not found']
      };
    }

    return validator.validate(tradeStrategy, tradeStrategySchema);
  } catch (error) {
    console.error('Trade strategy validation error:', error);
    return {
      isValid: false,
      errors: [`Trade strategy validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`]
    };
  }
}

/**
 * 验证市场指标部分
 */
export async function validateMarketIndicators(
  marketIndicators: any,
  env: Env
): Promise<ValidationResult> {
  try {
    // 获取完整schema并提取market_indicators部分
    const fullSchema = await schemaManager.getStrategyDefinitionSchema(env);
    const marketIndicatorsSchema = fullSchema?.properties?.market_indicators;

    if (!marketIndicatorsSchema) {
      return {
        isValid: false,
        errors: ['Market indicators schema not found']
      };
    }

    return validator.validate(marketIndicators, marketIndicatorsSchema);
  } catch (error) {
    console.error('Market indicators validation error:', error);
    return {
      isValid: false,
      errors: [`Market indicators validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`]
    };
  }
}

/**
 * 基础验证：检查基本结构（不依赖外部schema）
 */
export function validateBasicStructure(strategyDefinition: any): ValidationResult {
  const errors: string[] = [];

  if (!strategyDefinition || typeof strategyDefinition !== 'object') {
    errors.push('Strategy definition must be an object');
    return { isValid: false, errors };
  }

  // 检查必需的trade_strategy
  if (!strategyDefinition.trade_strategy) {
    errors.push('Missing required field: trade_strategy');
  } else if (typeof strategyDefinition.trade_strategy !== 'object') {
    errors.push('trade_strategy must be an object');
  } else {
    // 检查trade_strategy的基本结构
    const ts = strategyDefinition.trade_strategy;

    if (!ts.signals || !Array.isArray(ts.signals)) {
      errors.push('trade_strategy.signals must be an array');
    }

    if (!ts.outputs || typeof ts.outputs !== 'object') {
      errors.push('trade_strategy.outputs must be an object');
    } else {
      if (!ts.outputs.buy_signal || typeof ts.outputs.buy_signal !== 'string') {
        errors.push('trade_strategy.outputs.buy_signal must be a string');
      }
      if (!ts.outputs.sell_signal || typeof ts.outputs.sell_signal !== 'string') {
        errors.push('trade_strategy.outputs.sell_signal must be a string');
      }
    }
  }

  // 检查可选的market_indicators
  if (strategyDefinition.market_indicators) {
    if (typeof strategyDefinition.market_indicators !== 'object') {
      errors.push('market_indicators must be an object');
    } else {
      const mi = strategyDefinition.market_indicators;
      if (!mi.indicators || !Array.isArray(mi.indicators)) {
        errors.push('market_indicators.indicators must be an array');
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
