-- 修复定投组合的strategy_params类型（从字符串改为JSON对象）
UPDATE portfolios
SET parameters = jsonb_set(
    parameters,
    '{strategy_params}',
    '{}'::jsonb
)
WHERE code IN (
    'myinvestpilot_cc_dip_1',
    'myinvestpilot_cc_dip_2',
    'myinvestpilot_cn_dip_1',
    'myinvestpilot_cn_dip_2',
    'myinvestpilot_us_dip_1',
    'myinvestpilot_us_dip_2'
);

-- 验证更改
SELECT code, parameters->'strategy_params', jsonb_typeof(parameters->'strategy_params') as params_type
FROM portfolios
WHERE code IN (
    'myinvestpilot_cc_dip_1',
    'myinvestpilot_cc_dip_2',
    'myinvestpilot_cn_dip_1',
    'myinvestpilot_cn_dip_2',
    'myinvestpilot_us_dip_1',
    'myinvestpilot_us_dip_2'
); 