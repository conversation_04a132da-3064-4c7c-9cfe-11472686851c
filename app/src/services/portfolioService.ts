import { Context } from 'hono';
import {
  CreatePortfolioRequest,
  MarketGroup,
  Portfolio,
  StrategyGroup,
  UpdatePortfolioRequest,
  PortfolioConfigCodeStrategyMQ,
  PortfolioConfigDslStrategyMQ
} from '../types';
import { Redis } from "@upstash/redis/cloudflare";
import { query } from './databaseService';
import {
  TEMP_TOKEN_EXPIRY_MS,
  TEMP_TOKEN_EXPIRY_SECONDS,
  TEMP_TOKEN_MAX_DOWNLOADS,
  TEMP_TOKEN_PREFIX,
  API_BASE_PATH,
  TemporaryTokenPayload,
  validateTokenData
} from '../constants/tempToken';
import { sendToMQ } from './MQService';
import { getDBContext, getPortfolioConfig, getPortfolioDbUrl, isPortfolioUpdated, getPortfolioDataFromDbForProcessing } from '../utils/helpers';
import { constructPortfolioUpdateMessage, constructPortfolioTimeFlyMessage } from '../utils/messageConstructor';
import { checkUserAuthorization, createPortfolioInDatabase, softDeletePortfolio, updatePortfolioInDatabase, validatePortfolioCreation } from '../utils/portfolioDatabaseHelpers';

export async function updatePortfolio(c: Context) {
  const { code } = await c.req.json() as { code: string };
  const email = c.req.header('x-auth-user');

  if (!email) {
    return c.json({ error: 'User email not provided' }, 400);
  }

  const isAuthorized = await checkUserAuthorization(getDBContext(c), email, code);
  if (!isAuthorized) {
    return c.json({ error: 'Unauthorized to update this portfolio' }, 403);
  }

  const message = await constructPortfolioUpdateMessage(getDBContext(c), code);
	sendToMQ(c.env, c.executionCtx, message);
	return c.json({ message: 'Portfolio being processed. Please try again later.' }, 404);
}

export async function timeFly(c: Context) {
  const { code, year } = await c.req.json() as { code: string, year: string };
  const email = c.req.header('x-auth-user');

  if (!email) {
    return c.json({ error: 'User email not provided' }, 400);
  }

  try {
    const newCode = `${code}~${year}`;
    const portfolioConfig = await getPortfolioConfig(getDBContext(c), code);
		const message = constructPortfolioTimeFlyMessage(portfolioConfig, year, newCode);
		const isUpdated = await isPortfolioUpdated(c.env, newCode);

    if (!isUpdated) {
      sendToMQ(c.env, c.executionCtx, message);
      return c.json({ message: 'Portfolio being processed. Please try again later.' }, 404);
    } else {
      return c.json({
        message: 'success',
        data: {
          portfolio_db_url: getPortfolioDbUrl(newCode),
        }
      });
    }
  } catch (error) {
    if (error instanceof Error) {
      return c.json({ error: error.message }, 400);
    }
    return c.json({ error: 'An unexpected error occurred' }, 500);
  }
}

export async function getUserPortfolios(c: Context) {
  const email = c.req.header('x-auth-user');
  if (!email) {
    return c.json({ error: 'User email not provided' }, 400);
  }

  try {
    const result = await query(
			getDBContext(c),
			`
				SELECT p.* FROM strategy.portfolios p
				JOIN strategy.user_portfolios up ON p.id = up.portfolio_id
				WHERE up.email = $1 AND p.is_deleted = false AND p.is_official = false
			`,
			[email]
		);

    return c.json({ portfolios: result.rows });
  } catch (error) {
    console.error('Error fetching user portfolios:', error);
    return c.json({ error: 'An error occurred while fetching portfolios' }, 500);
  }
}

export async function getPortfolioDetails(c: Context) {
  const code = c.req.param('code');
  const email = c.req.header('x-auth-user');

  if (!email) {
    return c.json({ error: 'User email not provided' }, 400);
  }

  try {
    // 获取包含策略实现类型的组合数据
    const portfolioData = await getPortfolioDataFromDbForProcessing(getDBContext(c), code);

    if (!portfolioData.is_official) {
      const isAuthorized = await checkUserAuthorization(getDBContext(c), email, code);
      if (!isAuthorized) {
        return c.json({ error: 'Unauthorized to view this portfolio' }, 403);
      }
    }

    // 根据策略类型返回不同格式的响应
    let portfolioResponse: PortfolioConfigCodeStrategyMQ | PortfolioConfigDslStrategyMQ;

    // 判断是否为DSL策略：优先检查实际的DSL数据，其次检查策略类型
    const isDslStrategy = portfolioData.parameters_from_db.trade_strategy_dsl || portfolioData.strategy_implementation_type === 'DSL';

    if (isDslStrategy) {
      // DSL策略组合响应 - 验证数据完整性
      if (!portfolioData.parameters_from_db.trade_strategy_dsl) {
        console.error(`Data integrity issue: Portfolio ${portfolioData.code} is marked as DSL strategy but missing trade_strategy_dsl data`, {
          portfolioCode: portfolioData.code,
          strategyImplementationType: portfolioData.strategy_implementation_type,
          parametersKeys: Object.keys(portfolioData.parameters_from_db)
        });
        return c.json({
          error: 'Portfolio data integrity issue: DSL strategy data is missing. Please contact support.'
        }, 500);
      }

      portfolioResponse = {
        name: portfolioData.name,
        code: portfolioData.code,
        description: portfolioData.description,
        strategy_definition: {
          trade_strategy: portfolioData.parameters_from_db.trade_strategy_dsl,
          capital_strategy: {
            name: portfolioData.capital_strategy_name,
            params: portfolioData.parameters_from_db.capital_strategy_params || {}
          }
        },
        symbols: portfolioData.symbols_json,
        start_date: portfolioData.start_date,
        currency: portfolioData.currency,
        market: portfolioData.market,
        commission: portfolioData.commission,
        update_time: portfolioData.update_time,
        is_official: portfolioData.is_official,
        created_at: portfolioData.created_at,
        updated_at: portfolioData.updated_at,
        last_data_update_at: portfolioData.last_data_update_at,
        update_status: portfolioData.update_status
      } as PortfolioConfigDslStrategyMQ;
    } else {
      // 代码策略组合响应
      portfolioResponse = {
        name: portfolioData.name,
        code: portfolioData.code,
        description: portfolioData.description,
        strategy: {
          name: portfolioData.strategy_name,
          params: portfolioData.parameters_from_db.strategy_params || {}
        },
        capital_strategy: {
          name: portfolioData.capital_strategy_name,
          params: portfolioData.parameters_from_db.capital_strategy_params || {}
        },
        symbols: portfolioData.symbols_json,
        start_date: portfolioData.start_date,
        currency: portfolioData.currency,
        market: portfolioData.market,
        commission: portfolioData.commission,
        update_time: portfolioData.update_time,
        is_official: portfolioData.is_official,
        created_at: portfolioData.created_at,
        updated_at: portfolioData.updated_at,
        last_data_update_at: portfolioData.last_data_update_at,
        update_status: portfolioData.update_status
      } as PortfolioConfigCodeStrategyMQ;
    }

    return c.json(portfolioResponse);
  } catch (error) {
    if (error instanceof Error && error.message === 'Portfolio not found') {
      return c.json({ error: 'Portfolio not found' }, 404);
    }
    console.error('Error fetching portfolio config:', error);
    return c.json({ error: 'An unexpected error occurred' }, 500);
  }
}

export async function createCustomPortfolio(c: Context) {
  const email = c.req.header('x-auth-user');
  if (!email) {
    return c.json({ error: 'User email not provided' }, 400);
  }

  const data = await c.req.json() as CreatePortfolioRequest;

  const validationResult = await validatePortfolioCreation(getDBContext(c), email, data);
  if (validationResult.error) {
    return c.json({ error: validationResult.error }, 400);
  }

  try {
    const newPortfolio = await createPortfolioInDatabase(getDBContext(c), email, data);
    return c.json(newPortfolio, 201);
  } catch (error) {
    console.error('Error in portfolio creation:', error);
    return c.json({ error: 'An error occurred while creating the portfolio' }, 500);
  }
}

export async function updateCustomPortfolio(c: Context) {
  const email = c.req.header('x-auth-user');
  const code = c.req.param('updateCode');
  if (!email) {
    return c.json({ error: 'User email not provided' }, 400);
  }

  const data = await c.req.json() as UpdatePortfolioRequest;

  try {
    const updatedPortfolio = await updatePortfolioInDatabase(getDBContext(c), code, email, data);
    return c.json(updatedPortfolio);
  } catch (error) {
    if (error instanceof Error) {
      if (error.message === 'Portfolio not found or not owned by the user') {
        return c.json({ error: error.message }, 404);
      }
      return c.json({ error: error.message }, 400);
    }
    return c.json({ error: 'An unexpected error occurred' }, 500);
  }
}

export async function deleteCustomPortfolio(c: Context) {
  const email = c.req.header('x-auth-user');
  const code = c.req.param('deleteCode');
  if (!email) {
    return c.json({ error: 'User email not provided' }, 400);
  }

  const isAuthorized = await checkUserAuthorization(getDBContext(c), email, code);
  if (!isAuthorized) {
    return c.json({ error: 'Unauthorized to delete this portfolio' }, 403);
  }

  try {
    await softDeletePortfolio(getDBContext(c), code, email);
    return c.json({ message: 'Portfolio deleted successfully' });
  } catch (error) {
    console.error('Error deleting portfolio:', error);
    return c.json({ error: 'An error occurred while deleting the portfolio' }, 500);
  }
}

export async function getPublicPortfolioInfo(c: Context) {
  const code = c.req.param('publicCode');

  try {
    const sql = `
      SELECT code, name, description, currency, market, TO_CHAR(start_date, 'YYYY-MM-DD') as start_date, is_subscribable, is_deleted
      FROM strategy.portfolios
      WHERE code = $1
    `;
    const result = await query(getDBContext(c), sql, [code]);

    if (result.rows.length === 0) {
      return c.json({ error: 'Portfolio not found' }, 404);
    }

    return c.json(result.rows[0] as Portfolio);
  } catch (error) {
    console.error('Error fetching portfolio public info:', error);
    return c.json({ error: 'An error occurred while fetching portfolio information' }, 500);
  }
}

export async function getOfficialPortfolios(c: Context) {
  const groupBy = c.req.query('groupBy'); // 'strategy' or 'market'

  try {
    const sql = `
      SELECT s.id as strategy_id, s.name as strategy_name, s.description as strategy_description,
             p.code as portfolio_code, p.name as portfolio_name, p.description as portfolio_description,
						 p.market as market, p.currency as currency, TO_CHAR(p.start_date, 'YYYY-MM-DD') as start_date
      FROM strategy.strategies s
      JOIN strategy.portfolios p ON s.id = p.strategy_id
      WHERE s.type = 'TRADE' AND p.is_official = true AND p.is_deleted = false
      ORDER BY s.name, p.name
    `;

    const result = await query(getDBContext(c), sql, []);

    if (groupBy === 'market') {
      const marketGroups: MarketGroup[] = [];
      result.rows.forEach(row => {
        const market = row.portfolio_code.split('_')[1]; // Extract market from code
        let marketGroup = marketGroups.find(g => g.market === market);
        if (!marketGroup) {
          marketGroup = { market, official_portfolios: [] };
          marketGroups.push(marketGroup);
        }
        marketGroup.official_portfolios.push({
          code: row.portfolio_code,
          name: row.portfolio_name,
          description: row.portfolio_description,
					currency: row.currency,
					market: row.market,
					start_date: row.start_date
        });
      });
      return c.json(marketGroups);
    } else {
      // Default: group by strategy
      const strategyGroups = result.rows.reduce((acc: StrategyGroup[], row) => {
        let strategy = acc.find(s => s.strategy_id === row.strategy_id);
        if (!strategy) {
          strategy = {
            strategy_id: row.strategy_id,
            strategy_name: row.strategy_name,
            strategy_description: row.strategy_description,
            official_portfolios: []
          };
          acc.push(strategy);
        }
        strategy.official_portfolios.push({
          code: row.portfolio_code,
          name: row.portfolio_name,
          description: row.portfolio_description,
					currency: row.currency,
					market: row.market,
					start_date: row.start_date
        });
        return acc;
      }, []);
      return c.json(strategyGroups);
    }
  } catch (error) {
    console.error('Error fetching official portfolios:', error);
    return c.json({ error: 'An error occurred while fetching portfolios' }, 500);
  }
}

export async function getPortfolioSignalDatabase(c: Context) {
  const code = c.req.param('signalCode');
  const email = c.req.header('x-auth-user');

  if (!email) {
    return c.json({ error: 'User email not provided' }, 400);
  }

  try {
    const signalDbPath = `portfolios/${code}/${code}_signals.db`;

    const signalDb = await c.env.SIGNAL_BUCKET.get(signalDbPath);

    if (!signalDb) {
      return c.json({ error: 'Signal database not found' }, 404);
    }

    c.header('Content-Type', 'application/octet-stream');
    c.header('Content-Disposition', `attachment; filename="${code}_signals.db"`);

    return c.body(signalDb.body);
  } catch (error) {
    console.error('Error fetching portfolio signal database:', error);
    return c.json({ error: 'An error occurred while fetching the signal database' }, 500);
  }
}

export async function getPortfolioSignalPresignedUrl(c: Context) {
  const email = c.req.header('x-auth-user');
  const code = c.req.param('urlCode');

  if (!email) {
    return c.json({ error: 'User email not provided' }, 400);
  }

  // Check user authorization for the portfolio
  const isAuthorized = await checkUserAuthorization(getDBContext(c), email, code);
  if (!isAuthorized) {
    return c.json({ error: 'Unauthorized to access this portfolio signal database' }, 403);
  }

  try {
    // Generate temporary token
    const tempToken = crypto.randomUUID();
    const expiry = Date.now() + TEMP_TOKEN_EXPIRY_MS;

    // Store token data in Redis with automatic expiration
    const redis = Redis.fromEnv(c.env);
    const tokenData: TemporaryTokenPayload = {
      code,
      email,
      expiry,
      downloads: 0,
      maxDownloads: TEMP_TOKEN_MAX_DOWNLOADS
    };

    // Set with TTL - Redis will automatically delete the key
    await redis.setex(`${TEMP_TOKEN_PREFIX}${tempToken}`, TEMP_TOKEN_EXPIRY_SECONDS, JSON.stringify(tokenData));

    // Generate temporary public download URL using your domain with correct base path
    const origin = new URL(c.req.url).origin;
    const tempUrl = `${origin}${API_BASE_PATH}/portfolios/public/download_signal_db/${code}?temp_token=${tempToken}`;

    return c.json({ presignedUrl: tempUrl });
  } catch (error) {
    console.error('Error generating temporary download URL:', error);
    return c.json({ error: 'Failed to generate temporary download URL' }, 500);
  }
}

export async function getPortfolioSignalDatabasePublic(c: Context) {
  const code = c.req.param('symbol');
  const tempToken = c.req.query('temp_token');

  if (!tempToken) {
    return c.json({ error: 'Temporary token required' }, 400);
  }

  try {
    // Validate temporary token
    const redis = Redis.fromEnv(c.env);
    const tokenData = await redis.get(`${TEMP_TOKEN_PREFIX}${tempToken}`);

    if (!tokenData) {
      return c.json({ error: 'Download link expired or invalid' }, 403);
    }

    // Upstash Redis automatically deserializes JSON, so tokenData might already be an object
    const data = typeof tokenData === 'string' ? JSON.parse(tokenData) : tokenData;

    // Validate token data structure
    if (!validateTokenData(data)) {
      console.error('Invalid token data structure:', data);
      await redis.del(`${TEMP_TOKEN_PREFIX}${tempToken}`);
      return c.json({ error: 'Invalid temporary token structure' }, 403);
    }

    // Check if token is expired
    if (Date.now() > data.expiry) {
      await redis.del(`${TEMP_TOKEN_PREFIX}${tempToken}`);
      return c.json({ error: 'Download link expired' }, 403);
    }

    // Check download limit
    if (data.downloads >= data.maxDownloads) {
      return c.json({ error: 'Download limit exceeded' }, 403);
    }

    // Verify the portfolio code matches
    if (data.code !== code) {
      return c.json({ error: 'Invalid download token for this portfolio' }, 403);
    }

    // Increment download count
    data.downloads++;
    const remainingTTL = Math.floor((data.expiry - Date.now()) / 1000);
    await redis.setex(`${TEMP_TOKEN_PREFIX}${tempToken}`, remainingTTL, JSON.stringify(data));

    // Download the file
    const signalDbPath = `portfolios/${code}/${code}_signals.db`;
    const signalDb = await c.env.SIGNAL_BUCKET.get(signalDbPath);

    if (!signalDb) {
      return c.json({ error: 'Signal database not found' }, 404);
    }

    c.header('Content-Type', 'application/octet-stream');
    c.header('Content-Disposition', `attachment; filename="${code}_signals.db"`);

    return c.body(signalDb.body);
  } catch (error) {
    console.error('Error fetching public portfolio signal database:', error);
    return c.json({ error: 'An error occurred while fetching the signal database' }, 500);
  }
}
