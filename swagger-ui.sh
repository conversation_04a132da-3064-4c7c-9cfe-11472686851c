#!/bin/bash

# Swagger UI Server Launcher
# Quick script to start the Swagger UI server for API testing

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Swagger UI Server...${NC}"

# Check if Python is available
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo -e "${RED}❌ Python is not installed or not in PATH${NC}"
    echo "Please install Python 3.8+ and try again"
    exit 1
fi

# Use python3 if available, otherwise python
PYTHON_CMD="python3"
if ! command -v python3 &> /dev/null; then
    PYTHON_CMD="python"
fi

# Check if required packages are installed
echo -e "${YELLOW}📦 Checking dependencies...${NC}"
if ! $PYTHON_CMD -c "import flask, yaml, requests, flask_cors" 2>/dev/null; then
    echo -e "${YELLOW}⚠️  Missing dependencies. Installing...${NC}"

    # Try to install dependencies
    if [ -f "tool/requirements.txt" ]; then
        echo "Installing Swagger UI with CORS proxy dependencies..."
        pip install flask flask-cors pyyaml requests || {
            echo -e "${RED}❌ Failed to install dependencies${NC}"
            echo "Please run: pip install flask flask-cors pyyaml requests"
            exit 1
        }
    else
        echo "Installing minimal dependencies..."
        pip install flask flask-cors pyyaml requests || {
            echo -e "${RED}❌ Failed to install dependencies${NC}"
            echo "Please run: pip install flask flask-cors pyyaml requests"
            exit 1
        }
    fi
fi

# Check if OpenAPI spec exists
if [ ! -f "doc/api/openapi.yaml" ]; then
    echo -e "${RED}❌ OpenAPI specification not found at doc/api/openapi.yaml${NC}"
    exit 1
fi

echo -e "${GREEN}✅ All dependencies satisfied${NC}"
echo -e "${BLUE}🌐 Starting server...${NC}"

# Start the server
$PYTHON_CMD tool/serve_swagger.py
