/**
 * 通用AI Prompt管理器
 * 支持多种AI服务的prompt版本管理
 */

import { AiServiceType, PromptConfig, PromptVersionInfo } from './types';

// 静态导入所有prompt版本
import * as v10 from './prompts/strategy_generation/v1.0';
import * as v11 from './prompts/strategy_generation/v1.1';

// 静态导入映射
const PROMPT_MODULES: Record<string, { content: string }> = {
  'strategy_generation/v1.0': v10,
  'strategy_generation/v1.1': v11
};

// 所有AI服务的prompt配置
const AI_SERVICE_CONFIGS: Record<AiServiceType, PromptConfig> = {
  [AiServiceType.STRATEGY_GENERATION]: {
    serviceType: AiServiceType.STRATEGY_GENERATION,
    defaultVersion: 'v1.0',
    versions: {
      'v1.0': {
        name: 'v1.0',
        description: '完整版本 - 详细文档和复杂策略示例',
        file: 'strategy_generation/v1.0',
        stable: true,
        features: ['完整指标库', '复杂策略示例', '市场指标过滤', '股债轮动', '详细文档'],
        serviceType: AiServiceType.STRATEGY_GENERATION
      },
      'v1.1': {
        name: 'v1.1',
        description: '精简版本 - 核心功能和快速生成',
        file: 'strategy_generation/v1.1',
        stable: true,
        features: ['核心指标', '基础信号', '简洁示例', '快速响应'],
        serviceType: AiServiceType.STRATEGY_GENERATION
      }
    }
  }

  // 未来可扩展其他AI服务配置
};

// Prompt内容缓存
const promptCache = new Map<string, string>();

// 生成缓存键
function getCacheKey(serviceType: AiServiceType, version: string): string {
  return `${serviceType}:${version}`;
}

/**
 * 获取指定服务和版本的System Prompt
 */
export async function getSystemPrompt(
  serviceType: AiServiceType,
  version?: string
): Promise<string> {
  const config = AI_SERVICE_CONFIGS[serviceType];
  if (!config) {
    throw new Error(`Unknown AI service type: ${serviceType}`);
  }

  const targetVersion = version || config.defaultVersion;
  const cacheKey = getCacheKey(serviceType, targetVersion);

  // 检查缓存
  if (promptCache.has(cacheKey)) {
    return promptCache.get(cacheKey)!;
  }

  // 验证版本是否存在
  const versionInfo = config.versions[targetVersion];
  if (!versionInfo) {
    throw new Error(`Unknown version ${targetVersion} for service ${serviceType}`);
  }

  try {
    // 使用静态导入映射
    console.log(`Loading prompt ${serviceType}:${targetVersion} from ${versionInfo.file}...`);
    const module = PROMPT_MODULES[versionInfo.file];

    if (!module || !module.content) {
      throw new Error(`No content found for ${versionInfo.file}`);
    }

    // 缓存内容
    promptCache.set(cacheKey, module.content);
    console.log(`Successfully loaded and cached prompt ${serviceType}:${targetVersion}`);

    return module.content;
  } catch (error) {
    console.error(`Failed to load prompt ${serviceType}:${targetVersion}:`, error);

    // 降级到默认版本
    if (targetVersion !== config.defaultVersion) {
      console.warn(`Falling back to default version: ${config.defaultVersion}`);
      return getSystemPrompt(serviceType, config.defaultVersion);
    }

    throw new Error(`Failed to load prompt ${serviceType}:${targetVersion}: ${error}`);
  }
}

/**
 * 获取服务的版本信息
 */
export function getServiceVersionInfo(serviceType: AiServiceType, version: string): PromptVersionInfo {
  const config = AI_SERVICE_CONFIGS[serviceType];
  if (!config) {
    throw new Error(`Unknown AI service type: ${serviceType}`);
  }

  const versionInfo = config.versions[version];
  if (!versionInfo) {
    throw new Error(`Unknown version ${version} for service ${serviceType}`);
  }

  return versionInfo;
}

/**
 * 获取服务的所有可用版本
 */
export function getServiceVersions(serviceType: AiServiceType): Record<string, PromptVersionInfo> {
  const config = AI_SERVICE_CONFIGS[serviceType];
  if (!config) {
    throw new Error(`Unknown AI service type: ${serviceType}`);
  }

  return config.versions;
}

/**
 * 验证版本是否有效
 */
export function isValidVersion(serviceType: AiServiceType, version: string): boolean {
  const config = AI_SERVICE_CONFIGS[serviceType];
  return config && version in config.versions;
}

/**
 * 获取服务的默认版本
 */
export function getDefaultVersion(serviceType: AiServiceType): string {
  const config = AI_SERVICE_CONFIGS[serviceType];
  if (!config) {
    throw new Error(`Unknown AI service type: ${serviceType}`);
  }
  return config.defaultVersion;
}

/**
 * 清除缓存
 */
export function clearCache(): void {
  promptCache.clear();
}
