import { DBContext, Env, PortfolioDataFromDB } from '../types';
import { query } from '../services/databaseService';
import { PortfolioConfig } from '../types';
import { Context } from 'hono';

export function getDBContext(c: Context): DBContext {
	return {
		env: c.env,
		executionCtx: c.executionCtx
	};
}

export function generateUniquePortfolioCode(): string {
  const alphabet = 'abcdefghijklmnopqrstuvwxyz0123456789';
  const length = 8;
  const prefix = 'custom_';

  const array = new Uint8Array(length);
  crypto.getRandomValues(array);

  let result = prefix;
  for (let i = 0; i < length; i++) {
    result += alphabet[array[i] % alphabet.length];
  }

  return result;
}

export async function hasSubscribers(env: Env, portfolioCode: string): Promise<boolean> {
  try {
    const res = await env.TRADE_SIGNAL.fetch(`http://my-invest-trade-signal/get_subscriber_count/${portfolioCode}`, {
      method: "GET",
      headers: {
        'x-user-agent': 'my-invest-strategy-portfolio',
      }
    });

    if (!res.ok) {
      console.error(`Failed to get subscriber count for portfolio ${portfolioCode}: ${res.status} ${res.statusText}`);
      return false;
    }

    const data = await res.json() as { count: number };
    return data.count > 0;
  } catch (error) {
    console.error(`Error getting subscriber count for portfolio ${portfolioCode}:`, error);
    return false;
  }
}

// 获取包含策略实现类型的组合数据，用于消息构造
export async function getPortfolioDataFromDbForProcessing(c: DBContext, code: string): Promise<PortfolioDataFromDB> {
  const result = await query(c, `
    WITH portfolio_data AS (
      SELECT p.*,
             s1.name as strategy_name,
             s1.implementation_type as strategy_implementation_type,
             s2.name as capital_strategy_name
      FROM strategy.portfolios p
      JOIN strategy.strategies s1 ON p.strategy_id = s1.id
      JOIN strategy.strategies s2 ON p.capital_strategy_id = s2.id
      WHERE p.code = $1 AND p.is_deleted = false
    ),
    symbol_data AS (
      SELECT
        p.id as portfolio_id,
        json_agg(
          json_build_object('symbol', s.symbol, 'name', s.name)
          ORDER BY array_position(p.symbols, s.symbol)
        ) as symbols_json
      FROM portfolio_data p
      JOIN strategy.symbols s ON s.symbol = ANY(p.symbols) AND s.currency = p.currency
      GROUP BY p.id
    )
    SELECT
      p.*,
      p.strategy_name,
      p.strategy_implementation_type,
      p.capital_strategy_name,
      COALESCE(s.symbols_json, '[]'::json) as symbols_json
    FROM portfolio_data p
    LEFT JOIN symbol_data s ON p.id = s.portfolio_id;
  `, [code]);

  if (result.rows.length === 0) {
    throw new Error('Portfolio not found');
  }

  const portfolio = result.rows[0];

  return {
    id: portfolio.id,
    name: portfolio.name,
    code: portfolio.code,
    description: portfolio.description,
    strategy_id: portfolio.strategy_id,
    strategy_name: portfolio.strategy_name,
    strategy_implementation_type: portfolio.strategy_implementation_type,
    capital_strategy_id: portfolio.capital_strategy_id,
    capital_strategy_name: portfolio.capital_strategy_name,
    symbols: portfolio.symbols,
    start_date: portfolio.start_date.toISOString().split('T')[0],
    currency: portfolio.currency,
    market: portfolio.market,
    commission: parseFloat(portfolio.commission),
    update_time: portfolio.update_time,
    parameters_from_db: portfolio.parameters,
    is_official: portfolio.is_official,
    created_at: portfolio.created_at.toISOString(),
    updated_at: portfolio.updated_at.toISOString(),
    last_data_update_at: portfolio.last_data_update_at ? portfolio.last_data_update_at.toISOString() : null,
    update_status: portfolio.update_status,
    symbols_json: portfolio.symbols_json
  };
}

// 保持向后兼容的 getPortfolioConfig 函数
export async function getPortfolioConfig(c: DBContext, code: string): Promise<PortfolioConfig> {
  const result = await query(c, `
    WITH portfolio_data AS (
      SELECT p.*,
             s1.name as strategy_name,
             s2.name as capital_strategy_name
      FROM strategy.portfolios p
      JOIN strategy.strategies s1 ON p.strategy_id = s1.id
      JOIN strategy.strategies s2 ON p.capital_strategy_id = s2.id
      WHERE p.code = $1 AND p.is_deleted = false
    ),
    symbol_data AS (
      SELECT
        p.id as portfolio_id,
        json_agg(
          json_build_object('symbol', s.symbol, 'name', s.name)
          ORDER BY array_position(p.symbols, s.symbol)
        ) as symbols_json
      FROM portfolio_data p
      JOIN strategy.symbols s ON s.symbol = ANY(p.symbols) AND s.currency = p.currency
      GROUP BY p.id
    )
    SELECT
      p.*,
      p.strategy_name,
      p.capital_strategy_name,
      COALESCE(s.symbols_json, '[]'::json) as symbols_json
    FROM portfolio_data p
    LEFT JOIN symbol_data s ON p.id = s.portfolio_id;
  `, [code]);

  if (result.rows.length === 0) {
    throw new Error('Portfolio not found');
  }

  const portfolio = result.rows[0];

  return {
    name: portfolio.name,
    code: portfolio.code,
    description: portfolio.description,
    strategy: {
      name: portfolio.strategy_name,
      params: portfolio.parameters.strategy_params
    },
    capital_strategy: {
      name: portfolio.capital_strategy_name,
      params: portfolio.parameters.capital_strategy_params
    },
    symbols: portfolio.symbols_json,
    start_date: portfolio.start_date.toISOString().split('T')[0],
    currency: portfolio.currency,
    market: portfolio.market,
    commission: parseFloat(portfolio.commission),
    update_time: portfolio.update_time,
    is_official: portfolio.is_official,
    created_at: portfolio.created_at.toISOString(),
    updated_at: portfolio.updated_at.toISOString(),
    last_data_update_at: portfolio.last_data_update_at ? portfolio.last_data_update_at.toISOString() : null,
    update_status: portfolio.update_status
  };
}

export async function isPortfolioUpdated(env: Env, code: string): Promise<boolean> {
  const portfolio_db_path = `myinvestpilot/portfolios/${code}/${code}_portfolio.db`;
  const object = await env.BUCKET.head(portfolio_db_path);

  if (object?.uploaded) {
    return object.uploaded.toDateString() === new Date().toDateString();
  }

  return false;
}

export function getPortfolioDbUrl(code: string): string {
  const portfolio_db_path = `myinvestpilot/portfolios/${code}/${code}_portfolio.db`;
  return `https://media.i365.tech/${portfolio_db_path}`;
}
