openapi: 3.0.3
info:
  title: My Invest Strategy Portfolio API
  description: |
    API for managing investment strategies and portfolios, supporting both code-based and DSL-based strategies.

    ## Strategy Types

    This API supports two types of trading strategies:

    1. **Code Strategy**: References a pre-defined strategy by name with parameters
    2. **DSL Strategy**: Uses a custom DSL (Domain Specific Language) definition

    ## Authentication

    Authentication is handled by the API gateway. When testing through Swagger UI,
    ensure you are logged in to the main application in the same browser session.
    
  version: "1.0.0"
servers:
  - url: http://localhost:8888/api
    description: Local Proxy Server
tags:
  - name: Portfolio Management
    description: Operations related to creating, updating, and managing portfolios
  - name: Search
    description: Symbol searching operations
  - name: Public
    description: Publicly accessible portfolio information
  - name: AI
    description: AI-powered strategy generation and validation operations

components:

  schemas:
    StrategyConfig:
      type: object
      properties:
        name:
          type: string
          description: Name of the strategy
        params:
          type: object
          additionalProperties: true
          description: Strategy parameters (dynamic structure)
      required:
        - name
        - params

    TradeStrategy:
      type: object
      description: Dynamic DSL structure for trade strategy (array-based primitive format)
      properties:
        indicators:
          type: array
          description: Optional array of technical indicators
          items:
            type: object
            properties:
              id:
                type: string
                description: Unique identifier for the indicator
              type:
                type: string
                description: Dynamic indicator type (SMA, EMA, RSI, MACD, etc.)
              params:
                type: object
                description: Dynamic parameters specific to the indicator type
                additionalProperties: true
            required:
              - id
              - type
            additionalProperties: false
        signals:
          type: array
          description: Array of signal operations
          items:
            type: object
            properties:
              id:
                type: string
                description: Unique identifier for the signal
              type:
                type: string
                description: Dynamic signal type (GreaterThan, LessThan, Crossover, And, Or, etc.)
              epsilon:
                type: number
                description: Comparison tolerance value (optional)
              params:
                type: object
                description: Dynamic parameters specific to the signal type
                additionalProperties: true
              inputs:
                type: array
                description: Dynamic input structure for the signal
                items:
                  oneOf:
                    - type: object
                      properties:
                        ref:
                          type: string
                          description: Reference to an indicator ID
                      required: [ref]
                    - type: object
                      properties:
                        column:
                          type: string
                          description: Data column reference
                      required: [column]
                    - type: object
                      properties:
                        market:
                          type: string
                          description: Market indicator code
                        transformer:
                          type: string
                          description: Transformer name
                      required: [market, transformer]
                    - type: object
                      properties:
                        type:
                          type: string
                          enum: [Constant]
                        value:
                          type: number
                          description: Constant value
                      required: [type, value]
                    - type: number
                      description: Direct numeric value
            required:
              - id
              - type
            additionalProperties: false
        outputs:
          type: object
          description: Output signal mappings
          properties:
            buy_signal:
              type: string
              description: Reference to signal ID for buy decisions
            sell_signal:
              type: string
              description: Reference to signal ID for sell decisions
            indicators:
              type: array
              description: Optional indicator outputs
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: Indicator ID
                  output_name:
                    type: string
                    description: Output name for the indicator
                required: [id, output_name]
                additionalProperties: true
            market_indicators:
              type: array
              description: Optional market indicator outputs
              items:
                type: object
                properties:
                  market:
                    type: string
                    description: Market indicator code
                  transformer:
                    type: string
                    description: Transformer name
                  output_name:
                    type: string
                    description: Output name for the market indicator
                required: [market, transformer, output_name]
                additionalProperties: true
          required:
            - buy_signal
            - sell_signal
          additionalProperties: true
      required:
        - signals
        - outputs
      additionalProperties: true

    StrategyDefinition:
      type: object
      description: Complete strategy definition including trade strategy, capital strategy, and optional market indicators
      properties:
        market_indicators:
          type: object
          description: Optional market indicators configuration
          properties:
            indicators:
              type: array
              items:
                type: object
                properties:
                  code:
                    type: string
                    description: Market indicator code like VIX, SPX, 000300.SH
                  start_date:
                    type: string
                    format: date
                    description: Data start date (optional)
                  end_date:
                    type: string
                    format: date
                    description: Data end date (optional)
                additionalProperties: true
            transformers:
              type: array
              items:
                type: object
                properties:
                  name:
                    type: string
                    description: Transformer name for reference
                  type:
                    type: string
                    description: Dynamic transformer type
                  params:
                    type: object
                    description: Dynamic transformer parameters
                    additionalProperties: true
                additionalProperties: true
          additionalProperties: true
        trade_strategy:
          $ref: '#/components/schemas/TradeStrategy'
        capital_strategy:
          $ref: '#/components/schemas/StrategyConfig'
      required:
        - trade_strategy
        - capital_strategy
      additionalProperties: true

    StrategyDefinitionForApiInput:
      type: object
      properties:
        market_indicators:
          type: object
          description: Optional market indicators configuration
          properties:
            indicators:
              type: array
              items:
                type: object
                properties:
                  code:
                    type: string
                    description: Market indicator code (e.g., VIX, MOVE)
                  source:
                    type: string
                    description: Optional data source specification
                required: [code]
                additionalProperties: true
            transformers:
              type: array
              items:
                type: object
                properties:
                  name:
                    type: string
                    description: Unique name for the transformer
                  type:
                    type: string
                    description: Transformer type
                  params:
                    type: object
                    description: Parameters for the transformer
                    additionalProperties: true
                required: [name, type, params]
                additionalProperties: true
          required: [indicators]
          additionalProperties: true
        trade_strategy:
          $ref: '#/components/schemas/TradeStrategy'
        capital_strategy:
          $ref: '#/components/schemas/StrategyConfig'
      required:
        - trade_strategy
        - capital_strategy
      additionalProperties: true

    SymbolInfo:
      type: object
      properties:
        symbol:
          type: string
          description: Symbol code
        name:
          type: string
          description: Symbol name
      required:
        - symbol
        - name

    PortfolioUpdateStatus:
      type: string
      enum: [PENDING, COMPLETED, FAILED]
      description: Status of portfolio update process

    CreatePortfolioRequestCodeStrategy:
      type: object
      properties:
        name:
          type: string
          description: Portfolio name
        description:
          type: string
          description: Portfolio description
        symbols:
          type: array
          items:
            type: string
          description: Array of symbol codes
        start_date:
          type: string
          format: date
          description: Portfolio start date
        currency:
          type: string
          description: Portfolio currency
        market:
          type: string
          description: Market/Country
        commission:
          type: number
          format: float
          description: Commission rate
        update_time:
          type: string
          format: time
          example: "01:00:00"
          description: Daily update time
        strategy:
          $ref: '#/components/schemas/StrategyConfig'
        capital_strategy:
          $ref: '#/components/schemas/StrategyConfig'
      required:
        - name
        - symbols
        - start_date
        - currency
        - market
        - commission
        - update_time
        - strategy
        - capital_strategy

    CreatePortfolioRequestDslStrategy:
      type: object
      properties:
        name:
          type: string
          description: Portfolio name
        description:
          type: string
          description: Portfolio description
        symbols:
          type: array
          items:
            type: string
          description: Array of symbol codes
        start_date:
          type: string
          format: date
          description: Portfolio start date
        currency:
          type: string
          description: Portfolio currency
        market:
          type: string
          description: Market/Country
        commission:
          type: number
          format: float
          description: Commission rate
        update_time:
          type: string
          format: time
          example: "01:00:00"
          description: Daily update time
        strategy_definition:
          $ref: '#/components/schemas/StrategyDefinitionForApiInput'
      required:
        - name
        - symbols
        - start_date
        - currency
        - market
        - commission
        - update_time
        - strategy_definition

    PortfolioConfigCodeStrategyMQ:
      type: object
      properties:
        name:
          type: string
        code:
          type: string
        description:
          type: string
        strategy:
          $ref: '#/components/schemas/StrategyConfig'
        capital_strategy:
          $ref: '#/components/schemas/StrategyConfig'
        symbols:
          type: array
          items:
            $ref: '#/components/schemas/SymbolInfo'
        start_date:
          type: string
          format: date
        currency:
          type: string
        market:
          type: string
        commission:
          type: number
          format: float
        update_time:
          type: string
          format: time
        is_official:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        last_data_update_at:
          type: string
          format: date-time
          nullable: true
        update_status:
          $ref: '#/components/schemas/PortfolioUpdateStatus'

    PortfolioConfigDslStrategyMQ:
      type: object
      properties:
        name:
          type: string
        code:
          type: string
        description:
          type: string
        strategy_definition:
          $ref: '#/components/schemas/StrategyDefinitionForApiInput'
        symbols:
          type: array
          items:
            $ref: '#/components/schemas/SymbolInfo'
        start_date:
          type: string
          format: date
        currency:
          type: string
        market:
          type: string
        commission:
          type: number
          format: float
        update_time:
          type: string
          format: time
        is_official:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        last_data_update_at:
          type: string
          format: date-time
          nullable: true
        update_status:
          $ref: '#/components/schemas/PortfolioUpdateStatus'

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: Error message

    AiStrategyRequest:
      type: object
      properties:
        userInput:
          type: string
          description: |
            User input content - can be:
            1. Natural language description
            2. JSON strategy definition
            3. Mixed content (JSON + natural language instructions)
        promptVersion:
          type: string
          description: |
            Optional: Specify the System Prompt version to use.
            Available versions: 'v1.0', 'v1.1', 'v2.0-experimental'
            If not specified, uses the default version (v1.1)
          example: "v1.1"
      required:
        - userInput
      example:
        userInput: "我想要一个双均线策略，短期均线10天，长期均线30天，当短期均线上穿长期均线时买入，下穿时卖出"
        promptVersion: "v1.1"

    AiStrategyResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Whether the request was successful
        tradeStrategy:
          $ref: '#/components/schemas/TradeStrategy'
          description: Generated trade strategy DSL object (when successful)
        metadata:
          type: object
          description: Additional metadata about the generation process
          properties:
            promptVersion:
              type: string
              description: Version of the prompt used
              example: "v1.1"
            model:
              type: string
              description: AI model used for generation
              example: "gpt-4o"
            tokensUsed:
              type: integer
              description: Number of tokens consumed
              example: 1250
          additionalProperties: true
        error:
          type: string
          description: Error message (when failed)
      required:
        - success

    PromptVersionInfo:
      type: object
      properties:
        name:
          type: string
          description: Version identifier
          example: "v1.1"
        description:
          type: string
          description: Human-readable description of the version
          example: "增强版本 - 更多指标和优化建议"
        file:
          type: string
          description: Source file name for the version
          example: "v1.1.ts"
        stable:
          type: boolean
          description: Whether this is a stable version (true) or experimental (false)
          example: true
        features:
          type: array
          items:
            type: string
          description: List of key features in this version
          example: ["增强指标", "策略优化", "错误处理", "性能建议"]
      required:
        - name
        - description
        - file
        - stable
        - features

# Authentication is handled by the API gateway

paths:
  /portfolios:
    post:
      tags:
        - Portfolio Management
      summary: Create Custom Portfolio
      description: |
        Creates a new custom portfolio. Supports both code-based and DSL-based trading strategies.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/CreatePortfolioRequestCodeStrategy'
                - $ref: '#/components/schemas/CreatePortfolioRequestDslStrategy'
            examples:
              codeStrategy:
                summary: Code Strategy Example (Real API Data)
                value:
                  name: "A股test 1"
                  description: "测试组合"
                  symbols: ["159915", "159928", "159929"]
                  start_date: "2018-01-20"
                  currency: "CNY"
                  market: "China"
                  commission: 0.0001
                  update_time: "01:00:00"
                  strategy:
                    name: "DualMovingAverageStrategy"
                    params:
                      long_window: 24
                      short_window: 12
                  capital_strategy:
                    name: "PercentCapitalStrategy"
                    params:
                      percents: 30
                      initial_capital: 200000
              dslStrategy:
                summary: DSL Strategy Example (Real Primitive Format)
                value:
                  name: "A股1号(原语版)"
                  description: "基于原语化双均线策略的A股精选ETF组合"
                  symbols: ["159928", "159929", "510500", "159915", "159939"]
                  start_date: "2018-09-20"
                  currency: "CNY"
                  market: "China"
                  commission: 0.0001
                  update_time: "01:00:00"
                  strategy_definition:
                    trade_strategy:
                      indicators: [
                        {
                          "id": "shortMA",
                          "type": "SMA",
                          "params": {"period": 11, "column": "Close"}
                        },
                        {
                          "id": "longMA",
                          "type": "SMA",
                          "params": {"period": 22, "column": "Close"}
                        }
                      ]
                      signals: [
                        {
                          "id": "buy_signal",
                          "type": "Crossover",
                          "params": {"mode": "simple"},
                          "inputs": [{"ref": "shortMA"}, {"ref": "longMA"}]
                        },
                        {
                          "id": "sell_signal",
                          "type": "Crossunder",
                          "params": {"mode": "simple"},
                          "inputs": [{"ref": "shortMA"}, {"ref": "longMA"}]
                        }
                      ]
                      outputs:
                        buy_signal: "buy_signal"
                        sell_signal: "sell_signal"
                    capital_strategy:
                      name: "PercentCapitalStrategy"
                      params:
                        initial_capital: 100000
                        percents: 20
      responses:
        '201':
          description: Portfolio created successfully
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /portfolios/{code}:
    get:
      tags:
        - Portfolio Management
      summary: Get Portfolio Details
      parameters:
        - name: code
          in: path
          required: true
          schema:
            type: string
          description: Portfolio code
      responses:
        '200':
          description: Portfolio details retrieved successfully
        '404':
          description: Portfolio not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      tags:
        - Portfolio Management
      summary: Update Custom Portfolio
      parameters:
        - name: code
          in: path
          required: true
          schema:
            type: string
          description: Portfolio code
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/CreatePortfolioRequestCodeStrategy'
                - $ref: '#/components/schemas/CreatePortfolioRequestDslStrategy'
            examples:
              updateCodeStrategy:
                summary: Update with Code Strategy (Real API Data)
                value:
                  name: "A股2号更新版"
                  description: "基于优化双均线策略的A股精选ETF组合"
                  symbols: ["159915"]
                  start_date: "2019-01-01"
                  currency: "CNY"
                  market: "China"
                  commission: 0.00015
                  update_time: "02:00:00"
                  strategy:
                    name: "DualMovingAverageStrategy"
                    params:
                      long_window: 35
                      short_window: 10
                  capital_strategy:
                    name: "PercentCapitalStrategy"
                    params:
                      percents: 99
                      initial_capital: 120000
              updateDslStrategy:
                summary: Update with DSL Strategy (Real RSI Primitive)
                value:
                  name: "RSI超买超卖策略"
                  description: "基于RSI指标的简单超买超卖交易策略"
                  symbols: ["SPY", "QQQ"]
                  start_date: "2020-01-01"
                  currency: "USD"
                  market: "US"
                  commission: 0.0001
                  update_time: "08:00:00"
                  strategy_definition:
                    trade_strategy:
                      indicators: [
                        {
                          "id": "rsi_indicator",
                          "type": "RSI",
                          "params": {
                            "period": 11,
                            "column": "Close"
                          }
                        },
                        {
                          "id": "upper_threshold",
                          "type": "Constant",
                          "params": {
                            "value": 63
                          }
                        },
                        {
                          "id": "lower_threshold",
                          "type": "Constant",
                          "params": {
                            "value": 37
                          }
                        }
                      ]
                      signals: [
                        {
                          "id": "is_below_lower",
                          "type": "Comparison",
                          "params": {
                            "comparison": "less_equal"
                          },
                          "inputs": [
                            { "ref": "rsi_indicator" },
                            { "ref": "lower_threshold" }
                          ]
                        },
                        {
                          "id": "is_above_upper",
                          "type": "Comparison",
                          "params": {
                            "comparison": "greater_equal"
                          },
                          "inputs": [
                            { "ref": "rsi_indicator" },
                            { "ref": "upper_threshold" }
                          ]
                        },
                        {
                          "id": "buy_signal_cross",
                          "type": "CrossBelow",
                          "inputs": [
                            { "ref": "rsi_indicator" },
                            { "ref": "lower_threshold" }
                          ]
                        },
                        {
                          "id": "sell_signal_cross",
                          "type": "CrossAbove",
                          "inputs": [
                            { "ref": "rsi_indicator" },
                            { "ref": "upper_threshold" }
                          ]
                        }
                      ]
                      outputs:
                        buy_signal: "buy_signal_cross"
                        sell_signal: "sell_signal_cross"
                        indicators: [
                          { "id": "rsi_indicator", "output_name": "rsi" },
                          { "id": "upper_threshold", "output_name": "upper_bound" },
                          { "id": "lower_threshold", "output_name": "lower_bound" },
                          { "id": "is_below_lower", "output_name": "is_oversold" },
                          { "id": "is_above_upper", "output_name": "is_overbought" },
                          { "id": "buy_signal_cross", "output_name": "buy_signal" },
                          { "id": "sell_signal_cross", "output_name": "sell_signal" }
                        ]
                    capital_strategy:
                      name: "PercentCapitalStrategy"
                      params:
                        initial_capital: 100000
                        percents: 50
      responses:
        '200':
          description: Portfolio updated successfully
        '404':
          description: Portfolio not found

    delete:
      tags:
        - Portfolio Management
      summary: Delete Custom Portfolio
      description: Soft deletes a custom portfolio (marks as deleted)
      parameters:
        - name: code
          in: path
          required: true
          schema:
            type: string
          description: Portfolio code
      responses:
        '200':
          description: Portfolio deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Portfolio deleted successfully"
        '403':
          description: Unauthorized to delete this portfolio
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Portfolio not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /search_symbols:
    get:
      tags:
        - Search
      summary: Search Symbols
      security: []
      parameters:
        - name: q
          in: query
          required: true
          schema:
            type: string
          description: Search query
          example: "b"
        - name: c
          in: query
          schema:
            type: string
          description: Filter by currency
          example: "US"
        - name: t
          in: query
          schema:
            type: string
          description: Filter by type
          example: "CRYPTO"
      responses:
        '200':
          description: Search results retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  results:
                    type: array
                    items:
                      type: object
                      properties:
                        symbol:
                          type: string
                        name:
                          type: string
                        type:
                          type: string
                        market:
                          type: string
                        country:
                          type: string
                        currency:
                          type: string



  /update_portfolio:
    post:
      tags:
        - Portfolio Management
      summary: Trigger Portfolio Data Update
      description: Triggers an update data command for the specified portfolio's data, this will send a message to the data pipeline to update the portfolio's data.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: string
                  description: Portfolio code to update
                name:
                  type: string
                description:
                  type: string
                symbols:
                  type: array
                  items:
                    type: string
                strategy:
                  $ref: '#/components/schemas/StrategyConfig'
                capital_strategy:
                  $ref: '#/components/schemas/StrategyConfig'
                strategy_definition:
                  $ref: '#/components/schemas/StrategyDefinitionForApiInput'
              required:
                - code
            example:
              code: "custom_9b5k5evm"
      responses:
        '200':
          description: Portfolio updated successfully
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Portfolio not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /user_portfolios:
    get:
      tags:
        - Portfolio Management
      summary: Get User's Portfolios
      description: Retrieves all portfolios belonging to the authenticated user
      responses:
        '200':
          description: User portfolios retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    code:
                      type: string
                    name:
                      type: string
                    description:
                      type: string
                    is_official:
                      type: boolean

  /portfolios/public/{code}:
    get:
      tags:
        - Public
      summary: Get Public Portfolio by Code
      description: Retrieves public portfolio details by portfolio code
      security: []
      parameters:
        - name: code
          in: path
          required: true
          schema:
            type: string
          description: Portfolio code
          example: "myinvestpilot_cn_1"
      responses:
        '200':
          description: Public portfolio retrieved successfully
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/PortfolioConfigCodeStrategyMQ'
                  - $ref: '#/components/schemas/PortfolioConfigDslStrategyMQ'
        '404':
          description: Portfolio not found or not public
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /portfolios/signals/{code}:
    get:
      tags:
        - Portfolio Management
      summary: Download Portfolio Signal Database
      description: |
        Downloads the signal database file for a specific portfolio. The signal database contains
        trading signals and related data generated by the portfolio's strategy.

        **Authentication**: Handled by API gateway (same as other portfolio endpoints).

        **File Format**: Returns a SQLite database file (.db) as a binary download.
      parameters:
        - name: code
          in: path
          required: true
          description: Portfolio code to get signals for
          schema:
            type: string
          example: "myinvestpilot_cn_1"
      responses:
        '200':
          description: Signal database file downloaded successfully
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
                description: SQLite database file containing portfolio signals
          headers:
            Content-Disposition:
              description: Attachment filename for download
              schema:
                type: string
                example: 'attachment; filename="myinvestpilot_cn_1_signals.db"'
            Content-Type:
              description: Binary file content type
              schema:
                type: string
                example: "application/octet-stream"
        '400':
          description: User email not provided
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Signal database not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /official_portfolios:
    get:
      tags:
        - Public
      summary: Get Official Portfolios
      description: Retrieves all official portfolios grouped by strategy
      security: []
      responses:
        '200':
          description: Official portfolios retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    strategy_id:
                      type: string
                    strategy_name:
                      type: string
                    strategy_description:
                      type: string
                    official_portfolios:
                      type: array
                      items:
                        type: object
                        properties:
                          code:
                            type: string
                          name:
                            type: string
                          description:
                            type: string
                          currency:
                            type: string
                          market:
                            type: string
                          start_date:
                            type: string
                            format: date

  /portfolios/public/download_signal_db/{symbol}:
    get:
      tags:
        - Public
      summary: Download signal database
      description: |
        Public endpoint to download SQLite signal database file using temporary token.
        No authentication headers required - uses temporary token for access control.
        This endpoint is whitelisted in the API gateway.
      security: []
      parameters:
        - name: symbol
          in: path
          required: true
          schema:
            type: string
          description: Portfolio symbol identifying the signal database
          example: "myinvestpilot_cn_1"
        - name: temp_token
          in: query
          required: true
          schema:
            type: string
          description: Temporary download token (required for public access)
          example: "550e8400-e29b-41d4-a716-446655440000"
      responses:
        '200':
          description: Signal database file downloaded successfully
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              description: Attachment filename
              schema:
                type: string
                example: 'attachment; filename="myinvestpilot_cn_1_signals.db"'
        '400':
          description: Temporary token required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Invalid, expired, or limit exceeded token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Signal database not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Failed to fetch database
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /time_fly:
    post:
      tags:
        - Portfolio Management
      summary: Time Fly Operation
      description: Triggers time fly operation for portfolio processing
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: string
                  description: Portfolio code to process
                year:
                  type: string
                  description: Year for time fly operation (YYYY format)
                  pattern: "^[0-9]{4}$"
                  example: "2020"
              required:
                - code
            examples:
              timeFlyExample:
                summary: Time Fly Example (Real API Data)
                value:
                  code: "myinvestpilot_cn_1"
                  year: "2020"
      responses:
        '200':
          description: Time fly operation initiated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Time fly operation initiated"
                  job_id:
                    type: string
                    description: Job ID for tracking the operation
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Portfolio not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /ai/strategy/generate:
    post:
      tags:
        - AI
      summary: Generate Trading Strategy DSL
      description: |
        Uses AI to generate trading strategy DSL from natural language descriptions, JSON definitions, or mixed content.

        **Input Types Supported:**
        - **Natural Language**: Describe your strategy in plain language (Chinese or English)
        - **JSON Validation**: Provide incomplete JSON for validation and completion
        - **Mixed Content**: Combine JSON with natural language instructions

        **Prompt Versions:**
        - **v1.0**: Basic DSL generation with standard indicators
        - **v1.1**: Enhanced version with more indicators and optimization suggestions (default)
        - **v2.0-experimental**: AI-enhanced features and advanced functionality

        **Use Cases:**
        - Convert trading ideas to executable DSL
        - Validate and complete partial strategy definitions
        - Optimize existing strategies with AI suggestions
        - A/B test different prompt versions for strategy generation

        **Supported Indicators:**
        - SMA, EMA, RSI, MACD, Bollinger Bands, Constant values

        **Supported Signals:**
        - Comparison operations (LessThan, GreaterThan)
        - Crossover operations (CrossesAbove, CrossesBelow)
        - Logical operations (And, Or)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AiStrategyRequest'
            examples:
              naturalLanguage:
                summary: Natural Language Input (Chinese) - Default Version
                value:
                  userInput: "我想要一个双均线策略，短期均线10天，长期均线30天，当短期均线上穿长期均线时买入，下穿时卖出"
                  # promptVersion is optional - will use default v1.1
              naturalLanguageEnglish:
                summary: Natural Language Input (English) - Explicit Version
                value:
                  userInput: "I want a dual moving average strategy with 10-day short MA and 30-day long MA, buy when short MA crosses above long MA, sell when it crosses below"
                  promptVersion: "v1.1"
              rsiStrategy:
                summary: RSI Strategy Request - Stable Version
                value:
                  userInput: "Create an RSI strategy with 14-period RSI, buy when RSI is below 30 (oversold), sell when RSI is above 70 (overbought)"
                  promptVersion: "v1.0"
              withVersionSelection:
                summary: With Specific Prompt Version
                value:
                  userInput: "创建一个AI增强的动量策略，包含风险控制和自适应参数"
                  promptVersion: "v2.0-experimental"
              jsonValidation:
                summary: JSON Validation/Completion - Enhanced Version
                value:
                  userInput: '{"indicators": [{"id": "rsi14", "type": "RSI", "params": {"period": 14, "column": "Close"}}], "signals": [], "outputs": {}}'
                  promptVersion: "v1.1"
              mixedContent:
                summary: Mixed Content (JSON + Instructions) - Default Version
                value:
                  userInput: '{"indicators": [{"id": "sma_short", "type": "SMA", "params": {"period": 10, "column": "Close"}}, {"id": "sma_long", "type": "SMA", "params": {"period": 30, "column": "Close"}}], "signals": [{"id": "golden_cross", "type": "CrossesAbove", "inputs": [{"ref": "sma_short"}, {"ref": "sma_long"}]}, {"id": "death_cross", "type": "CrossesBelow", "inputs": [{"ref": "sma_short"}, {"ref": "sma_long"}]}], "outputs": {"buy_signal": "golden_cross", "sell_signal": "death_cross"}}\n\n请帮我把上面的策略参数修改成短期5天，长期20天'
                  # promptVersion omitted - will use default v1.1
      responses:
        '200':
          description: Strategy DSL generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AiStrategyResponse'
              examples:
                successfulGeneration:
                  summary: Successful Strategy Generation
                  value:
                    success: true
                    tradeStrategy:
                      indicators:
                        - id: "sma_short"
                          type: "SMA"
                          params:
                            period: 10
                            column: "Close"
                        - id: "sma_long"
                          type: "SMA"
                          params:
                            period: 30
                            column: "Close"
                      signals:
                        - id: "golden_cross"
                          type: "CrossesAbove"
                          inputs:
                            - ref: "sma_short"
                            - ref: "sma_long"
                        - id: "death_cross"
                          type: "CrossesBelow"
                          inputs:
                            - ref: "sma_short"
                            - ref: "sma_long"
                      outputs:
                        buy_signal: "golden_cross"
                        sell_signal: "death_cross"
                    metadata:
                      promptVersion: "v1.1"
                      model: "gpt-4o"
                      tokensUsed: 1250
                rsiExample:
                  summary: RSI Strategy Example
                  value:
                    success: true
                    tradeStrategy:
                      indicators:
                        - id: "rsi14"
                          type: "RSI"
                          params:
                            period: 14
                            column: "Close"
                        - id: "oversold_threshold"
                          type: "Constant"
                          params:
                            value: 30
                        - id: "overbought_threshold"
                          type: "Constant"
                          params:
                            value: 70
                      signals:
                        - id: "oversold_signal"
                          type: "LessThan"
                          inputs:
                            - ref: "rsi14"
                            - ref: "oversold_threshold"
                        - id: "overbought_signal"
                          type: "GreaterThan"
                          inputs:
                            - ref: "rsi14"
                            - ref: "overbought_threshold"
                      outputs:
                        buy_signal: "oversold_signal"
                        sell_signal: "overbought_signal"
                    metadata:
                      promptVersion: "v1.1"
                      model: "gpt-4o"
                      tokensUsed: 1580
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AiStrategyResponse'
              examples:
                invalidRequest:
                  summary: Invalid Request Example
                  value:
                    success: false
                    error: "Missing required field: userInput"
        '500':
          description: AI processing error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AiStrategyResponse'
              examples:
                aiError:
                  summary: AI Processing Error
                  value:
                    success: false
                    error: "Failed to parse AI response as JSON: Unexpected token"
                configError:
                  summary: Configuration Error
                  value:
                    success: false
                    error: "Internal server error. Please check server logs for details."

  /ai/strategy/prompt_versions:
    get:
      tags:
        - AI
      summary: Get Strategy Generation Prompt Versions
      description: |
        Retrieves all available System Prompt versions specifically for strategy generation.

        **Version Types:**
        - **Stable versions**: Recommended for production use
        - **Experimental versions**: Latest features, may be less stable

        **Use Cases:**
        - Version discovery for strategy generation
        - A/B testing different prompt versions
        - Feature exploration and comparison
      security: []
      responses:
        '200':
          description: Available prompt versions retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  serviceType:
                    type: string
                    example: "strategy_generation"
                  versions:
                    type: object
                    additionalProperties:
                      $ref: '#/components/schemas/PromptVersionInfo'
                  default:
                    type: string
                    description: Default version used when no version is specified
                    example: "v1.1"
              examples:
                successfulResponse:
                  summary: Strategy Generation Versions
                  value:
                    success: true
                    serviceType: "strategy_generation"
                    default: "v1.1"
                    versions:
                      "v1.0":
                        name: "v1.0"
                        description: "基础版本 - 标准DSL生成"
                        file: "strategy_generation/v1.0.ts"
                        stable: true
                        features: ["基础指标", "标准信号", "简单策略"]
                        serviceType: "strategy_generation"
                      "v1.1":
                        name: "v1.1"
                        description: "精简版本 - 核心功能和快速生成"
                        file: "strategy_generation/v1.1.ts"
                        stable: true
                        features: ["核心指标", "基础信号", "简洁示例", "快速响应"]
                        serviceType: "strategy_generation"
        '500':
          description: Server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error:
                    type: string
                    example: "Failed to get prompt versions"

  /ai/strategy/prompt_versions/{version}:
    get:
      tags:
        - AI
      summary: Get Strategy Generation Prompt Version Content
      description: |
        Retrieves both the actual System Prompt content and metadata for a specific strategy generation version.

        **Returns:**
        - Complete System Prompt text content
        - Version metadata (name, description, features, etc.)

        **Use Cases:**
        - Preview prompt content before using in strategy generation
        - Compare different prompt versions
        - Debug and analyze prompt differences
        - Client-side prompt caching and optimization
      security: []
      parameters:
        - name: version
          in: path
          required: true
          schema:
            type: string
            enum: ["v1.0", "v1.1"]
          description: Prompt version identifier
          example: "v1.1"
      responses:
        '200':
          description: Version content and information retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  version:
                    $ref: '#/components/schemas/PromptVersionInfo'
                  content:
                    type: string
                    description: Complete System Prompt content for this version
                    example: "# 量化交易策略DSL生成系统 v1.1 (增强版)\n\n你是一个专业的量化策略分析师..."
              examples:
                stableVersion:
                  summary: Stable Version (v1.1) with Content
                  value:
                    success: true
                    version:
                      name: "v1.1"
                      description: "精简版本 - 核心功能和快速生成"
                      file: "strategy_generation/v1.1.ts"
                      stable: true
                      features: ["核心指标", "基础信号", "简洁示例", "快速响应"]
                      serviceType: "strategy_generation"
                    content: "# 投资策略原语系统助手\n\n你是投资策略原语系统的AI助手，负责帮助用户创建和优化基于原语的交易策略配置。\n\n## 核心组件\n\n### 1. 指标原语\n从价格数据计算技术指标，输出时间序列数据。\n- **SMA/EMA**：移动平均线\n- **RSI**：相对强弱指标\n\n[... 完整的prompt内容 ...]"
        '400':
          description: Invalid version
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error:
                    type: string
                    example: "Invalid version: v999"
        '500':
          description: Server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error:
                    type: string
                    example: "Failed to get version info"

  /portfolios/signals_url/{code}:
    get:
      tags:
        - Portfolio Management
      summary: Generate temporary download URL for signal database
      description: |
        Generates a temporary public download URL for a portfolio's signal database.
        The URL expires in 30 minutes and has a download limit of 3 times.
        Requires user authorization to generate the temporary URL.
      parameters:
        - name: code
          in: path
          required: true
          schema:
            type: string
          description: Portfolio code identifying the signal database, must be your own portfolio
          example: "YOUR_PORTFOLIO_CODE"
      responses:
        '200':
          description: Temporary download URL generated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  presignedUrl:
                    type: string
                    example: https://api.myinvestpilot.com/strategy_portfolio/portfolios/public/download_signal_db/myinvestpilot_cn_1?temp_token=550e8400-e29b-41d4-a716-446655440000
        '400':
          description: User email not provided
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Unauthorized to access this portfolio
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Failed to generate temporary download URL
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
