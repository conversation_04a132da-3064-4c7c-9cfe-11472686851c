-- First, alter the table to increase the size of the currency column
ALTER TABLE portfolios ALTER COLUMN currency TYPE VARCHAR(10);
ALTER TABLE symbols ALTER COLUMN currency TYPE VARCHAR(10);

-- Update the cryptocurrency portfolios
UPDATE portfolios
SET currency = 'USDT', market = 'Crypto'
WHERE code IN ('myinvestpilot_cc_1', 'myinvestpilot_cc_2');

-- Update the cryptocurrency symbols
UPDATE symbols
SET currency = 'USDT'
WHERE symbol IN ('BTC', 'ETH', 'XRP', 'LTC', 'BCH', 'ADA', 'DOT', 'BNB', 'LINK', 'XLM', 'SOL', 'DOGE', 'TRX', 'AVAX', 'UNI')
  AND type = 'CRYPTO';
