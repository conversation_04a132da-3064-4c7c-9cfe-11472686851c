/**
 * 模块化AI路由系统
 * 支持多种AI服务的扩展
 */

import { Hono } from 'hono';
import { AiServiceType } from '../types';
import {
  getServiceVersions,
  getServiceVersionInfo,
  isValidVersion,
  getSystemPrompt,
  getDefaultVersion
} from '../promptManager';
import { StrategyGenerationService } from '../services/strategyGenerationService';

const router = new Hono();

// 服务实例
const strategyService = new StrategyGenerationService();

// ==================== 策略生成相关路由 ====================

// AI辅助策略生成端点
router.post('/strategy/generate', async (c) => {
  const requestBody = await c.req.json();
  return strategyService.processRequest(c, requestBody);
});

// 获取策略生成的prompt版本
router.get('/strategy/prompt_versions', async (c) => {
  try {
    const versions = getServiceVersions(AiServiceType.STRATEGY_GENERATION);
    return c.json({
      success: true,
      serviceType: AiServiceType.STRATEGY_GENERATION,
      versions: versions,
      default: getDefaultVersion(AiServiceType.STRATEGY_GENERATION)
    });
  } catch (error) {
    console.error('Error getting strategy prompt versions:', error);
    return c.json({
      success: false,
      error: 'Failed to get prompt versions'
    }, 500);
  }
});

// 获取策略生成的特定版本内容
router.get('/strategy/prompt_versions/:version', async (c) => {
  try {
    const version = c.req.param('version');

    if (!isValidVersion(AiServiceType.STRATEGY_GENERATION, version)) {
      return c.json({
        success: false,
        error: `Invalid version: ${version}`
      }, 400);
    }

    const versionInfo = getServiceVersionInfo(AiServiceType.STRATEGY_GENERATION, version);
    const promptContent = await getSystemPrompt(AiServiceType.STRATEGY_GENERATION, version);

    return c.json({
      success: true,
      version: versionInfo,
      content: promptContent
    });
  } catch (error) {
    console.error('Error getting strategy version info:', error);
    return c.json({
      success: false,
      error: 'Failed to get version info'
    }, 500);
  }
});

// ==================== 未来扩展区域 ====================
// 投资组合优化、风险分析等功能可以在这里添加

export { router as aiRoutes };
