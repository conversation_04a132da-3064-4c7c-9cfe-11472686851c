# myInvestStrategyPortfolio App

A Cloudflare Workers application for managing investment strategy portfolios with AI-assisted strategy generation.

## Overview

This application provides APIs for creating, managing, and analyzing investment portfolios with support for both traditional code-based strategies and modern DSL (Domain Specific Language) strategies. It includes AI-powered strategy generation capabilities using OpenAI GPT models.

## Features

### Core Functionality
- **Portfolio Management**: Create, update, and manage investment portfolios
- **Symbol Search**: Search for stocks, ETFs, and crypto symbols with filtering
- **Strategy Support**: Both traditional code-based and modern DSL strategies
- **AI Integration**: AI-powered strategy generation using OpenAI GPT models

### Strategy Types
- **Code Strategy**: Reference pre-defined strategies by name with parameters
- **DSL Strategy**: Custom Domain Specific Language for flexible strategy definition

### AI-Assisted Strategy Generation
- **Natural Language Input**: Describe strategies in plain English or Chinese
- **JSON Validation**: Validate and complete partial strategy definitions
- **Mixed Content**: Combine JSON with natural language instructions
- **Multiple Prompt Versions**: Different AI models optimized for various use cases

## Technology Stack

### Backend
- **Cloudflare Workers**: Serverless runtime environment
- **Hono**: Fast web framework for Cloudflare Workers
- **TypeScript**: Type-safe development

### AI Integration
- **OpenAI GPT-4o**: AI model for strategy generation
- **Cloudflare AI Gateway**: Optional proxy for AI API calls (configurable)

### Database & Storage
- **D1 Database**: SQLite-compatible serverless database
- **Upstash Redis**: Caching and session storage
- **Upstash Kafka**: Message queue for portfolio updates

## API Documentation

For complete API documentation including endpoints, request/response formats, and interactive testing, see:
- **OpenAPI Specification**: `doc/api/openapi.yaml`
- **Interactive Testing**: Use the Swagger UI server in `tool/` directory

## Development

### Prerequisites
- Node.js 18+
- Cloudflare Workers CLI (`wrangler`)

### Local Development
1. Clone the repository
2. Install dependencies: `npm install`
3. Configure environment variables in `.dev.vars`
4. Start development server: `npm run dev`

### Environment Configuration
Copy `.dev.vars` and fill in your values:
```bash
OPENAI_API_KEY=your_openai_api_key
AI_GATEWAY_URL=https://gateway.ai.cloudflare.com/v1/{account_id}/{gateway_id}/openai/chat/completions
DB_URL=your_database_url
# ... other configuration
```

## Testing

### AI Testing Tool
Use the Python testing script to test AI strategy generation:

```bash
cd tool
python test_ai.py --auth <EMAIL>
```

This will test multiple prompt versions and generate an HTML report for manual evaluation.

### API Testing
Use the Swagger UI for interactive API testing:

```bash
cd tool
python serve_swagger.py
# Open http://localhost:8888 in browser
```

## Deployment

### Production Deployment
```bash
# Set secrets
wrangler secret put OPENAI_API_KEY
wrangler secret put AI_GATEWAY_URL  # optional

# Deploy
npm run deploy
```

### Environment Variables
- `OPENAI_API_KEY`: Required for AI functionality
- `AI_GATEWAY_URL`: Optional Cloudflare AI Gateway proxy URL
- `DB_URL`: Database connection string
- `UPSTASH_REDIS_REST_TOKEN`: Redis cache token
- `UPSTASH_KAFKA_*`: Message queue configuration

## Architecture

### Key Components
- **Portfolio Management**: CRUD operations for investment portfolios
- **Strategy Engine**: Support for both code-based and DSL strategies
- **AI Service**: GPT-powered strategy generation with multiple prompt versions
- **Message Queue**: Asynchronous portfolio updates via Kafka
- **Caching**: Redis-based caching for performance

### API Design
- RESTful endpoints with consistent response formats
- Authentication via `x-auth-user` header
- Comprehensive error handling with appropriate HTTP status codes
- OpenAPI 3.0 specification for documentation

## License

MIT License - see LICENSE file for details.
