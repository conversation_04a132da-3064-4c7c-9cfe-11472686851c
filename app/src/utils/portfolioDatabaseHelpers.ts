import { query, withTransaction } from "../services/databaseService";
import {
  CreatePortfolioRequest,
  CreatePortfolioRequestCodeStrategy,
  CreatePortfolioRequestDslStrategy,
  DBContext,
  PortfolioUpdateStatus,
  UpdatePortfolioRequest,
  UpdatePortfolioRequestCodeStrategy,
  UpdatePortfolioRequestDslStrategy
} from "../types";
import { generateUniquePortfolioCode } from "./helpers";
import { validateStrategyDefinition, validateBasicStructure } from '../services/schemaValidationService';

export async function getPortfoliosForUpdate(c: DBContext, limit: number, offset: number): Promise<any[]> {
  const result = await query(c,
	`
    SELECT
			code,
			update_status,
			last_data_update_at,
			last_update_attempt,
			update_time,
			is_official
		FROM strategy.portfolios
		WHERE is_deleted = false
			AND (
				-- 确保当前时间在预定的更新时间之后
				CURRENT_TIME >= update_time
			)
			AND (
				-- 从未更新过的组合
				last_data_update_at IS NULL
				OR
				last_update_attempt IS NULL
				OR
				-- 今天还没有成功更新的组合
				(DATE(COALESCE(last_data_update_at, '1970-01-01'::timestamp)) < CURRENT_DATE)
				OR
				-- 更新失败的组合（状态不是 COMPLETED）
				(update_status != $3)
			)
			AND (
				-- 不在进行中的更新
				update_status != $4
				OR
				-- 或者上次尝试更新已经超过15分钟
				last_update_attempt IS NULL
				OR
				last_update_attempt < NOW() - INTERVAL '15 minutes'
			)
		ORDER BY
			-- 首先按官方组合排序
			is_official DESC,
			-- 然后优先选择从未更新过的组合
			CASE
				WHEN last_data_update_at IS NULL OR last_update_attempt IS NULL THEN 0
				ELSE 1
			END,
			-- 最后按最后更新日期升序排序
			COALESCE(last_data_update_at, '1970-01-01'::timestamp) ASC
		LIMIT $1 OFFSET $2
  `, [limit, offset, PortfolioUpdateStatus.COMPLETED, PortfolioUpdateStatus.PENDING]);

  return result.rows;
}

export async function validatePortfolioCreation(c: DBContext, email: string, data: CreatePortfolioRequest) {
  // 基础业务逻辑验证
  const userPortfoliosCount = await query(
		c,
    'SELECT COUNT(*) FROM strategy.user_portfolios WHERE email = $1 AND is_deleted = false',
    [email]
  );
  if (userPortfoliosCount.rows[0].count >= 6) {
    return { error: 'You have reached the maximum limit of 6 custom portfolios' };
  }

  const startDate = new Date(data.start_date);
  if (startDate < new Date('2016-01-01')) {
    return { error: 'The start date must be on or after January 1, 2016' };
  }

  if (data.symbols.length > 10) {
    return { error: 'A maximum of 10 symbols are allowed' };
  }

  // DSL策略的schema验证
  if ('strategy_definition' in data) {
    const dslData = data as CreatePortfolioRequestDslStrategy;

    // 基础结构验证（不依赖外部schema）
    const basicValidation = validateBasicStructure(dslData.strategy_definition);
    if (!basicValidation.isValid) {
      return { error: `Strategy definition validation failed: ${basicValidation.errors.join(', ')}` };
    }

    // 动态schema验证（使用R2中的schema）
    try {
      const schemaValidation = await validateStrategyDefinition(dslData.strategy_definition, c.env);
      if (!schemaValidation.isValid) {
        console.warn('Dynamic schema validation failed:', schemaValidation.errors);
        // 注意：这里我们记录警告但不阻止创建，因为外部schema可能暂时不可用
        // 如果你希望严格验证，可以改为 return { error: ... }
      }
    } catch (error) {
      console.error('Schema validation error:', error);
      // 同样，这里可以选择是否要阻止创建
    }
  }

  return { error: null };
}

export async function checkUserAuthorization(c: DBContext, email: string, portfolioCode: string): Promise<boolean> {
  const result = await query(
		c,
    'SELECT * FROM strategy.user_portfolios WHERE email = $1 AND portfolio_id = (SELECT id FROM strategy.portfolios WHERE code = $2) AND is_deleted = false',
    [email, portfolioCode]
  );

  return result.rows.length > 0;
}

export async function createPortfolioInDatabase(c: DBContext, email: string, data: CreatePortfolioRequest) {
  return withTransaction(c, async (client) => {
    // 生成唯一的组合代码
    let newCode;
    let isUnique = false;
    while (!isUnique) {
      newCode = generateUniquePortfolioCode();
      const existingPortfolio = await client.query(
        'SELECT code FROM strategy.portfolios WHERE code = $1',
        [newCode]
      );
      isUnique = existingPortfolio.rows.length === 0;
    }

    let strategyId: string;
    let capitalStrategyId: string;
    let parameters: any;

    // 判断是代码策略还是DSL策略
    if ('strategy_definition' in data) {
      // DSL策略组合
      const dslData = data as CreatePortfolioRequestDslStrategy;

      // 获取通用DSL策略ID
      const dslStrategyResult = await client.query(
        'SELECT id FROM strategy.strategies WHERE name = $1 AND implementation_type = $2',
        ['UserDefinedDSLTradeStrategy', 'DSL']
      );
      if (dslStrategyResult.rows.length === 0) {
        throw new Error('DSL strategy not found. Please ensure the database migration has been applied.');
      }
      strategyId = dslStrategyResult.rows[0].id;

      // 验证资金管理策略存在
      const capitalStrategyResult = await client.query(
        'SELECT id FROM strategy.strategies WHERE name = $1',
        [dslData.strategy_definition.capital_strategy.name]
      );
      if (capitalStrategyResult.rows.length === 0) {
        throw new Error('Invalid capital strategy name');
      }
      capitalStrategyId = capitalStrategyResult.rows[0].id;

      // 准备参数 - DSL格式
      parameters = {
        trade_strategy_dsl: dslData.strategy_definition.trade_strategy,
        capital_strategy_params: dslData.strategy_definition.capital_strategy.params
      };

      // 添加market_indicators（如果存在）
      if (dslData.strategy_definition.market_indicators) {
        parameters.market_indicators = dslData.strategy_definition.market_indicators;
      }
    } else {
      // 代码策略组合
      const codeData = data as CreatePortfolioRequestCodeStrategy;

      // 验证交易策略存在
      const strategyResult = await client.query(
        'SELECT id FROM strategy.strategies WHERE name = $1',
        [codeData.strategy.name]
      );
      if (strategyResult.rows.length === 0) {
        throw new Error('Invalid strategy name');
      }
      strategyId = strategyResult.rows[0].id;

      // 验证资金管理策略存在
      const capitalStrategyResult = await client.query(
        'SELECT id FROM strategy.strategies WHERE name = $1',
        [codeData.capital_strategy.name]
      );
      if (capitalStrategyResult.rows.length === 0) {
        throw new Error('Invalid capital strategy name');
      }
      capitalStrategyId = capitalStrategyResult.rows[0].id;

      // 准备参数 - 代码格式
      parameters = {
        strategy_params: codeData.strategy.params,
        capital_strategy_params: codeData.capital_strategy.params
      };
    }

    // 创建组合
    const result = await client.query(
      `INSERT INTO strategy.portfolios (
        name, code, description, strategy_id, capital_strategy_id,
        symbols, start_date, currency, market, commission, update_time,
        parameters, is_official, is_deleted
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, false, false)
      RETURNING *`,
      [
        data.name, newCode, data.description, strategyId, capitalStrategyId,
        data.symbols, data.start_date, data.currency, data.market, data.commission,
        data.update_time, JSON.stringify(parameters)
      ]
    );

    const newPortfolio = result.rows[0];

    // 关联用户和组合
    await client.query(
      `INSERT INTO strategy.user_portfolios (email, portfolio_id, is_deleted)
       VALUES ($1, $2, false)`,
      [email, newPortfolio.id]
    );

    // 清理返回数据
    newPortfolio.is_deleted = undefined;
    return newPortfolio;
  });
}

export async function updatePortfolioInDatabase(c: DBContext, code: string, email: string, data: UpdatePortfolioRequest) {
  return withTransaction(c, async (client) => {
    // 检查组合存在且属于该用户
    const portfolioCheck = await client.query(
      `SELECT p.id, p.parameters
      FROM strategy.portfolios p
      JOIN strategy.user_portfolios up ON p.id = up.portfolio_id
      WHERE p.code = $1 AND up.email = $2 AND p.is_deleted = false`,
      [code, email]
    );

    if (portfolioCheck.rows.length === 0) {
      throw new Error('Portfolio not found or not owned by the user');
    }

    const portfolioId = portfolioCheck.rows[0].id;
    let parameters = portfolioCheck.rows[0].parameters;

    // 构建动态更新查询
    let updateQuery = 'UPDATE strategy.portfolios SET ';
    const updateValues = [];
    let valueIndex = 1;

    // 基础字段更新
    const simpleUpdates = {
      name: data.name,
      description: data.description,
      symbols: data.symbols,
      start_date: data.start_date,
      currency: data.currency,
      market: data.market,
      commission: data.commission,
      update_time: data.update_time
    };

    for (const [field, value] of Object.entries(simpleUpdates)) {
      if (value !== undefined) {
        updateQuery += `${field} = $${valueIndex}, `;
        updateValues.push(value);
        valueIndex++;
      }
    }

    // 判断是代码策略还是DSL策略更新
    if ('strategy_definition' in data) {
      // DSL策略组合更新
      const dslData = data as UpdatePortfolioRequestDslStrategy;

      if (dslData.strategy_definition) {
        // 更新为DSL策略
        const dslStrategyResult = await client.query(
          'SELECT id FROM strategy.strategies WHERE name = $1 AND implementation_type = $2',
          ['UserDefinedDSLTradeStrategy', 'DSL']
        );
        if (dslStrategyResult.rows.length === 0) {
          throw new Error('DSL strategy not found. Please ensure the database migration has been applied.');
        }
        const strategyId = dslStrategyResult.rows[0].id;
        updateQuery += `strategy_id = $${valueIndex}, `;
        updateValues.push(strategyId);
        valueIndex++;

        // 验证资金管理策略存在
        const capitalStrategyResult = await client.query(
          'SELECT id FROM strategy.strategies WHERE name = $1',
          [dslData.strategy_definition.capital_strategy.name]
        );
        if (capitalStrategyResult.rows.length === 0) {
          throw new Error('Invalid capital strategy name');
        }
        const capitalStrategyId = capitalStrategyResult.rows[0].id;
        updateQuery += `capital_strategy_id = $${valueIndex}, `;
        updateValues.push(capitalStrategyId);
        valueIndex++;

        // 更新参数 - DSL格式，完全重新初始化以确保数据清洁
        // 这会自动清理任何旧的代码策略参数（strategy_params）
        parameters = {
          trade_strategy_dsl: dslData.strategy_definition.trade_strategy,
          capital_strategy_params: dslData.strategy_definition.capital_strategy.params
        };

        // 添加market_indicators（如果存在）
        if (dslData.strategy_definition.market_indicators) {
          parameters.market_indicators = dslData.strategy_definition.market_indicators;
        }
      }
    } else {
      // 代码策略组合更新
      const codeData = data as UpdatePortfolioRequestCodeStrategy;

      // 检查是否从DSL策略转换为代码策略，如果是则需要清理DSL数据
      const isConvertingFromDsl = parameters.trade_strategy_dsl !== undefined;

      // 如果从DSL转换为代码策略，清理所有DSL相关数据
      if (isConvertingFromDsl) {
        // 创建新的parameters对象，只保留非策略相关的数据
        const cleanParameters: any = {};
        // 保留任何非策略相关的参数（如果有的话）
        for (const [key, value] of Object.entries(parameters)) {
          if (key !== 'trade_strategy_dsl' && key !== 'strategy_params' && key !== 'capital_strategy_params') {
            cleanParameters[key] = value;
          }
        }
        parameters = cleanParameters;
      }

      // 更新交易策略
      if (codeData.strategy) {
        const strategyResult = await client.query(
          'SELECT id FROM strategy.strategies WHERE name = $1',
          [codeData.strategy.name]
        );
        if (strategyResult.rows.length === 0) {
          throw new Error('Invalid strategy name');
        }
        const strategyId = strategyResult.rows[0].id;
        updateQuery += `strategy_id = $${valueIndex}, `;
        updateValues.push(strategyId);
        valueIndex++;

        parameters.strategy_params = codeData.strategy.params;
      }

      // 更新资金管理策略
      if (codeData.capital_strategy) {
        const capitalStrategyResult = await client.query(
          'SELECT id FROM strategy.strategies WHERE name = $1',
          [codeData.capital_strategy.name]
        );
        if (capitalStrategyResult.rows.length === 0) {
          throw new Error('Invalid capital strategy name');
        }
        const capitalStrategyId = capitalStrategyResult.rows[0].id;
        updateQuery += `capital_strategy_id = $${valueIndex}, `;
        updateValues.push(capitalStrategyId);
        valueIndex++;

        parameters.capital_strategy_params = codeData.capital_strategy.params;
      }
    }

    // 更新参数和时间戳
    updateQuery += `parameters = $${valueIndex}, `;
    updateValues.push(JSON.stringify(parameters));
    valueIndex++;

    updateQuery += `updated_at = CURRENT_TIMESTAMP WHERE id = $${valueIndex} RETURNING *`;
    updateValues.push(portfolioId);

    const result = await client.query(updateQuery, updateValues);
    return result.rows[0];
  });
}

export async function softDeletePortfolio(c: DBContext, code: string, email: string) {
  return withTransaction(c, async (client) => {
    // 检查组合存在且属于该用户
    const portfolioCheck = await client.query(
      `SELECT p.id, p.is_official
      FROM strategy.portfolios p
      JOIN strategy.user_portfolios up ON p.id = up.portfolio_id
      WHERE p.code = $1 AND up.email = $2 AND p.is_deleted = false`,
      [code, email]
    );

    if (portfolioCheck.rows.length === 0) {
      throw new Error('Portfolio not found or not owned by the user');
    }

    const portfolio = portfolioCheck.rows[0];

    if (portfolio.is_official) {
      throw new Error('Cannot delete an official portfolio');
    }

    // 软删除组合
    await client.query(
      'UPDATE strategy.portfolios SET is_deleted = true, updated_at = CURRENT_TIMESTAMP WHERE id = $1',
      [portfolio.id]
    );

    // 软删除用户-组合关联
    await client.query(
      'UPDATE strategy.user_portfolios SET is_deleted = true, updated_at = CURRENT_TIMESTAMP WHERE portfolio_id = $1 AND email = $2',
      [portfolio.id, email]
    );

    return { message: 'Portfolio successfully deleted' };
  });
}

export async function updatePortfolioStatus(c: DBContext, portfolioId: string, status: PortfolioUpdateStatus) {
  let queryStatment: string;
  let params: any[];

  switch (status) {
    case PortfolioUpdateStatus.COMPLETED:
      queryStatment = `
        UPDATE strategy.portfolios
        SET update_status = $1,
            last_data_update_at = CURRENT_TIMESTAMP
        WHERE code = $2
      `;
      params = [status, portfolioId];
      break;

    case PortfolioUpdateStatus.PENDING:
      queryStatment = `
        UPDATE strategy.portfolios
        SET update_status = $1,
            last_update_attempt = CURRENT_TIMESTAMP
        WHERE code = $2
      `;
      params = [status, portfolioId];
      break;

    case PortfolioUpdateStatus.FAILED:
      queryStatment = `
        UPDATE strategy.portfolios
        SET update_status = $1 WHERE code = $2
      `;
      params = [status, portfolioId];
      break;

    default:
      throw new Error(`Invalid portfolio update status: ${status}`);
  }
  await query(c, queryStatment, params);
}
