#!/usr/bin/env python3

"""
发送AI生成的策略到MQ进行回测验证
"""

import argparse
import json
import os
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List
from dotenv import load_dotenv
from upstash_redis import Redis

# 加载环境变量
load_dotenv()

# 固定测试ID映射，避免重复数据
TEST_CASE_IDS = {
    '基础双均线策略（中文）': 'test_dual_ma_cn',
    'Basic Dual MA Strategy (English)': 'test_dual_ma_en', 
    'RSI策略': 'test_rsi_strategy',
    '复杂多指标策略': 'test_complex_multi',
    'JSON补全': 'test_json_completion',
    '混合内容': 'test_mixed_content'
}

def generate_portfolio_config(trade_strategy: dict, test_case_name: str, version: str) -> dict:
    """生成完整的组合配置"""
    # 使用固定ID避免重复数据
    base_id = TEST_CASE_IDS.get(test_case_name, f"test_{test_case_name.lower().replace(' ', '_')}")
    portfolio_id = f"{base_id}_{version}"
    
    return {
        "name": f"AI测试策略-{test_case_name}-{version}",
        "code": portfolio_id,
        "description": f"AI生成的测试策略: {test_case_name} (版本: {version})",
        "strategy_definition": {
            "trade_strategy": trade_strategy,
            "capital_strategy": {
                "name": "PercentCapitalStrategy",
                "params": {
                    "initial_capital": 100000,
                    "percents": 95
                }
            }
        },
        "symbols": [
            {"symbol": "159915", "name": "创业板ETF"},
            {"symbol": "513100", "name": "纳指ETF"},
            {"symbol": "518880", "name": "黄金ETF"}
        ],
        "start_date": "2023-01-01",
        "market": "China",
        "currency": "CNY",
        "commission": 0.0001,
        "update_time": "01:00",
        "is_official": False,
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat(),
        "last_data_update_at": None,
        "update_status": "PENDING"
    }

def create_mq_message(portfolio_config: dict) -> dict:
    """创建MQ消息"""
    return {
        "job_id": str(uuid.uuid4()),
        "timestamp": datetime.now().isoformat(),
        "action": "create_or_update",
        "portfolio_config": portfolio_config
    }

def send_to_redis(message: dict) -> bool:
    """发送消息到Redis队列"""
    try:
        redis = Redis(
            url=os.getenv('UPSTASH_REDIS_REST_URL'),
            token=os.getenv('UPSTASH_REDIS_REST_TOKEN')
        )
        
        queue_name = "invest-strategy-service-ingestion-queue"
        redis.rpush(queue_name, json.dumps(message))
        return True
    except Exception as e:
        print(f"❌ Redis发送失败: {e}")
        return False

def process_results_file(file_path: str) -> None:
    """处理AI测试结果文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        strategies = json.load(f)
    
    print(f"📁 处理文件: {file_path}")
    print(f"📊 找到 {len(strategies)} 个成功的策略")
    print()
    
    sent_count = 0
    for strategy in strategies:
        test_case = strategy['test_case']
        version = strategy['version']
        trade_strategy = strategy['trade_strategy']
        
        print(f"🧪 处理: {test_case} ({version})")
        
        # 生成组合配置
        portfolio_config = generate_portfolio_config(trade_strategy, test_case, version)
        portfolio_id = portfolio_config['code']
        
        # 创建MQ消息
        mq_message = create_mq_message(portfolio_config)
        
        # 发送到Redis
        success = send_to_redis(mq_message)
        
        if success:
            print(f"✅ 已发送到MQ，组合ID: {portfolio_id}")
            sent_count += 1
        else:
            print(f"❌ 发送失败")
        print()
    
    print(f"🎉 处理完成！成功发送 {sent_count}/{len(strategies)} 个策略到MQ")

def main():
    parser = argparse.ArgumentParser(description='发送AI策略到MQ进行回测验证')
    parser.add_argument('file', nargs='?', help='AI测试结果文件路径')
    parser.add_argument('--latest', action='store_true', help='处理最新的测试结果文件')
    
    args = parser.parse_args()
    
    if args.latest:
        # 查找最新的测试结果文件
        results_dir = Path(__file__).parent / 'test-results'
        json_files = list(results_dir.glob('ai-test-*.json'))
        if not json_files:
            print("❌ 未找到测试结果文件")
            print("💡 请先运行 python test_ai.py 生成测试结果")
            return
        
        latest_file = max(json_files, key=lambda f: f.stat().st_mtime)
        file_path = str(latest_file)
    elif args.file:
        file_path = args.file
    else:
        print("❌ 请指定文件路径或使用 --latest 参数")
        print("💡 使用方法:")
        print("   python send_to_mq.py --latest")
        print("   python send_to_mq.py test-results/ai-test-2024-01-01_12-00-00.json")
        return
    
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        return
    
    # 检查环境变量
    if not os.getenv('UPSTASH_REDIS_REST_URL') or not os.getenv('UPSTASH_REDIS_REST_TOKEN'):
        print("❌ 缺少Redis配置，请检查.env文件中的UPSTASH_REDIS_REST_URL和UPSTASH_REDIS_REST_TOKEN")
        return
    
    print("🚀 开始发送AI策略到MQ")
    print(f"📁 文件: {file_path}")
    print(f"🔗 Redis: {os.getenv('UPSTASH_REDIS_REST_URL')}")
    print()
    
    process_results_file(file_path)

if __name__ == '__main__':
    main()
