name = "my-invest-strategy-portfolio"
main = "./src/index.ts"
compatibility_date = "2024-11-11"
compatibility_flags = ["nodejs_compat"]

workers_dev = false

tail_consumers = [
  { service = "my-invest-log", environment = "production" }
]

services = [
  { binding = "TRADE_SIGNAL", service = "my-invest-trade-signal" }
]

[[r2_buckets]]
binding = 'BUCKET'
bucket_name = 'media-i365-tech'
preview_bucket_name = 'media-i365-tech'

[[r2_buckets]]
binding = 'SIGNAL_BUCKET'
bucket_name = 'myinvestpilot'

[[queues.producers]]
queue = "my-invest-strategy-portfolio"
binding = "QUEUE"

[[queues.consumers]]
queue = "my-invest-strategy-portfolio"
max_batch_size = 10
max_batch_timeout = 30
max_retries = 2
dead_letter_queue = "my-invest-strategy-portfolio-dlq"
max_concurrency = 1

[[hyperdrive]]
binding = "HYPERDRIVE"
id = "1d3b6da291d8436f90527dc07c3e7906"

[triggers]
crons = [
  "*/10 * * * *", # 每10分钟执行一次
  "0 0 */2 * *"   # 每2天执行一次（在每第二天的 00:00 UTC）
]

[placement]
mode = "smart"

[observability]
enabled = true
head_sampling_rate = 1

[vars]
SERVICE_NAME = "my-invest-strategy-portfolio"
MQ_TOPIC_NAME = "invest-strategy-service-ingestion-queue"
UPSTASH_REDIS_REST_URL = "https://rare-lizard-21313.upstash.io"

# The necessary secrets are:
# - UPSTASH_REDIS_REST_TOKEN
# Run `echo <VALUE> | wrangler secret put <NAME>` for each of these

# Additional secrets for AI functionality:
# - OPENAI_API_KEY (required)
# - AI_GATEWAY_URL (optional, for Cloudflare AI Gateway proxy)
#
# Example commands:
# wrangler secret put OPENAI_API_KEY
# wrangler secret put AI_GATEWAY_URL
#
# Example AI Gateway URL format:
# https://gateway.ai.cloudflare.com/v1/{account_id}/{gateway_id}/openai/chat/completions
