DROP INDEX IF EXISTS strategy.idx_symbols_symbol;
DROP INDEX IF EXISTS strategy.idx_symbols_name;
DROP INDEX IF EXISTS strategy.idx_symbols_symbol_trgm;
DROP INDEX IF EXISTS strategy.idx_symbols_name_trgm;
DROP INDEX IF EXISTS strategy.idx_symbols_is_deleted;

CREATE EXTENSION IF NOT EXISTS pg_trgm WITH SCHEMA public;

CREATE INDEX idx_symbols_symbol ON strategy.symbols(symbol);
CREATE INDEX idx_symbols_name ON strategy.symbols(name);
CREATE INDEX idx_symbols_symbol_trgm ON strategy.symbols USING gin (symbol public.gin_trgm_ops);
CREATE INDEX idx_symbols_name_trgm ON strategy.symbols USING gin (name public.gin_trgm_ops);
CREATE INDEX idx_symbols_is_deleted ON strategy.symbols(is_deleted) WHERE is_deleted = false;
