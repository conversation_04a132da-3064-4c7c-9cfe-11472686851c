name: Sync Symbols

on:
  schedule:
    - cron: '0 0 * * 0'  # Run every Sunday at midnight UTC
  workflow_dispatch:  # This allows manual triggering

jobs:
  sync:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2

    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.10'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r tool/requirements.txt

    - name: Run sync script
      env:
        DATABASE_URL: ${{ secrets.DATABASE_URL }}
        ALPHAVANTAGE_API_KEY: ${{ secrets.ALPHAVANTAGE_API_KEY }}
        TUSHARE_API_TOKEN: ${{ secrets.TUSHARE_API_TOKEN }}
      run: python tool/sync_symbols.py