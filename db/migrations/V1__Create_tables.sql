-- Create ENUM types
CREATE TYPE strategy_type AS ENUM ('TRADE', 'CAPITAL');
CREATE TYPE implementation_type AS ENUM ('CODE', 'DSL');
CREATE TYPE symbol_type AS ENUM ('STOCK', 'ETF', 'CRYPTO');
CREATE TYPE update_status AS ENUM ('PENDING', 'COMPLETED', 'FAILED');

-- Create tables
CREATE TABLE strategies (
    id UUID PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    type strategy_type NOT NULL,
    description TEXT,
    implementation_type implementation_type NOT NULL,
    dsl_script TEXT,
    parameters JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE portfolios (
    id UUID PRIMARY KEY,
    name VARCHA<PERSON>(255) NOT NULL,
    code VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    strategy_id UUID REFERENCES strategies(id),
    capital_strategy_id UUID REFERENCES strategies(id),
    symbols TEXT[] NOT NULL DEFAULT '{}',
    start_date DATE NOT NULL,
    currency VARCHAR(3) NOT NULL,
    market VARCHAR(50) NOT NULL,
    commission DECIMAL(5,4) NOT NULL,
    update_time TIME NOT NULL,
    parameters JSONB,
    is_official BOOLEAN DEFAULT false,
    is_deleted BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_data_update_at TIMESTAMP WITH TIME ZONE,
    update_status update_status DEFAULT 'PENDING',
    last_update_attempt TIMESTAMP WITH TIME ZONE
);

CREATE TABLE symbols (
    id UUID PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    type symbol_type NOT NULL,
    market VARCHAR(50) NOT NULL,
    country VARCHAR(50),
    currency VARCHAR(3) NOT NULL,
    is_deleted BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, market)
);
