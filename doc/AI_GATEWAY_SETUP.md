# Cloudflare AI Gateway Setup

This document explains how to configure the application to use Cloudflare AI Gateway as a proxy for OpenAI API calls.

## Benefits of Using AI Gateway

- **Cost Tracking**: Monitor and track AI API usage and costs
- **Rate Limiting**: Control API request rates
- **Caching**: Cache responses to reduce costs and improve performance
- **Analytics**: Detailed analytics on AI API usage
- **Security**: Additional layer of security and monitoring

## Configuration

### 1. Environment Variables

The application supports two modes:

#### Direct OpenAI Connection (Default)
```bash
OPENAI_API_KEY=your_openai_api_key_here
# AI_GATEWAY_URL is not set
```

#### Via Cloudflare AI Gateway (Recommended)
```bash
OPENAI_API_KEY=your_openai_api_key_here
AI_GATEWAY_URL=https://gateway.ai.cloudflare.com/v1/{account_id}/{gateway_id}/openai/chat/completions
```

### 2. Setting Up Cloudflare AI Gateway

1. **Login to Cloudflare Dashboard**
   - Go to [Cloudflare Dashboard](https://dash.cloudflare.com/)
   - Navigate to "AI" → "AI Gateway"

2. **Create a Gateway**
   - Click "Create Gateway"
   - Name: `myinvestpilot` (or your preferred name)
   - Click "Create"

3. **Get Gateway URL**
   - After creation, you'll see the gateway URL format:
   - `https://gateway.ai.cloudflare.com/v1/{account_id}/{gateway_id}/openai/chat/completions`
   - Replace `{account_id}` and `{gateway_id}` with your actual values

### 3. Deployment Configuration

#### For Cloudflare Workers (Production)

```bash
# Set the OpenAI API key
wrangler secret put OPENAI_API_KEY
# Enter your OpenAI API key when prompted

# Set the AI Gateway URL (optional)
wrangler secret put AI_GATEWAY_URL
# Enter your gateway URL when prompted
```

#### For Local Development

Fill in the values in `app/.dev.vars` file:

```bash
OPENAI_API_KEY=your_openai_api_key_here
AI_GATEWAY_URL=https://gateway.ai.cloudflare.com/v1/your_account_id/your_gateway_id/openai/chat/completions
```

## Usage Examples

### Example Gateway URL
```
https://gateway.ai.cloudflare.com/v1/20cf736b0cc331fdc0fd15533bb1a2dc/myinvestpilot/openai/chat/completions
```

### Testing the Configuration

You can test the gateway configuration using curl:

```bash
curl -X POST https://gateway.ai.cloudflare.com/v1/20cf736b0cc331fdc0fd15533bb1a2dc/myinvestpilot/openai/chat/completions \
  --header 'Authorization: Bearer YOUR_OPENAI_API_KEY' \
  --header 'Content-Type: application/json' \
  --data '{
    "model": "gpt-4o",
    "messages": [
      {
        "role": "user",
        "content": "Hello, this is a test message."
      }
    ]
  }'
```

## Monitoring and Analytics

Once configured, you can monitor your AI API usage through the Cloudflare Dashboard:

1. Go to "AI" → "AI Gateway"
2. Click on your gateway name
3. View analytics including:
   - Request volume
   - Token usage
   - Cost tracking
   - Error rates
   - Response times

## Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Check that your `OPENAI_API_KEY` is correct
   - Ensure the API key has sufficient permissions

2. **404 Not Found**
   - Verify the `AI_GATEWAY_URL` format is correct
   - Check that `{account_id}` and `{gateway_id}` are replaced with actual values

3. **Fallback to Direct Connection**
   - If `AI_GATEWAY_URL` is not set, the application will use direct OpenAI connection
   - Check logs for "Using AI API endpoint" messages

### Debug Logging

The application logs which endpoint it's using:
```
Using AI API endpoint: https://gateway.ai.cloudflare.com/v1/[REDACTED]/myinvestpilot/openai/chat/completions
```

## Security Notes

- Never commit API keys to version control
- Use Cloudflare Workers secrets for production deployment
- The AI Gateway URL itself is not sensitive, but the API key is
- Consider using different gateways for different environments (dev/staging/prod)
