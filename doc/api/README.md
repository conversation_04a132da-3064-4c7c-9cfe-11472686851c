# API Documentation

This directory contains the API documentation and testing resources for the My Invest Strategy Portfolio API.

## Files

- **`openapi.yaml`** - Complete OpenAPI 3.0.3 specification with interactive Swagger UI
- **`API_TESTING.md`** - Comprehensive testing guide and examples
- **`AUTHENTICATION.md`** - Authentication setup instructions

## API Overview

The My Invest Strategy Portfolio API supports two types of trading strategies:

### 1. Code Strategy (Traditional)
References pre-defined strategies by name with separate parameters:
```json
{
  "strategy": {
    "name": "DualMovingAverageStrategy",
    "params": { "short_window": 10, "long_window": 30 }
  },
  "capital_strategy": {
    "name": "PercentCapitalStrategy", 
    "params": { "percents": 20, "initial_capital": 100000 }
  }
}
```

### 2. DSL Strategy (New)
Uses embedded DSL definitions with internal parameters:
```json
{
  "strategy_definition": {
    "trade_strategy": {
      "indicators": {
        "sma_short": { "type": "SMA", "period": 10 },
        "sma_long": { "type": "SMA", "period": 30 }
      },
      "signals": {
        "buy": "sma_short > sma_long",
        "sell": "sma_short < sma_long"
      },
      "outputs": {
        "position": "if(buy, 1, if(sell, 0, prev(position)))"
      }
    },
    "capital_strategy": {
      "name": "PercentCapitalStrategy",
      "params": { "percents": 20, "initial_capital": 100000 }
    }
  }
}
```

## Quick Start

1. **Start Swagger UI server**:
   ```bash
   npm run swagger:serve
   ```

2. **Open browser**: <http://localhost:8888>

3. **Authenticate**: Click "Authorize" and enter your email

4. **Test endpoints**: Use the interactive interface

## Key Features

- 🔄 **Strategy Type Conversion**: Convert between Code and DSL strategies
- 🧹 **Data Hygiene**: Automatic cleanup of stale parameters during conversion
- 🛡️ **Data Integrity**: Robust validation and error handling
- 📊 **Comprehensive Testing**: Full test coverage for both strategy types
- 🔐 **Secure R2 Access**: Generate presigned URLs for private SQLite databases

## Server Environments

- **Production**: `https://api.myinvestpilot.com/strategy_portfolio`
- **Development**: `http://localhost:8787/strategy_portfolio`

## Authentication

The API supports two authentication methods:

### 🍪 Cookie Authentication (Recommended)
- **Method**: JWT token in browser cookie
- **Usage**: Login to `https://api.myinvestpilot.com` first, then use Swagger UI
- **Automatic**: No manual configuration needed in browser testing

### 📧 Header Authentication (Alternative)
- **Method**: Email address in `x-auth-user` header
- **Usage**: Manual configuration in Swagger UI "Authorize" section
- **Fallback**: For cases where cookie auth doesn't work

See `AUTHENTICATION.md` for detailed setup instructions.

## Testing

See `API_TESTING.md` for detailed testing instructions and examples.
