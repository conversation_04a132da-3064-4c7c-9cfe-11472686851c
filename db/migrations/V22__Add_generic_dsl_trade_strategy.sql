-- V22__Add_generic_dsl_trade_strategy.sql
-- 添加通用的DSL交易策略记录，用于支持用户自定义DSL策略组合

-- 确保 uuid-ossp 扩展已启用，gen_random_uuid() 函数需要它
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

INSERT INTO strategy.strategies (id, name, type, description, implementation_type, dsl_script, parameters, created_at, updated_at)
VALUES (
    gen_random_uuid(),
    'UserDefinedDSLTradeStrategy',
    'TRADE',
    '【交易策略】用户自定义DSL交易策略：这是一个通用的占位符策略，用于支持用户通过DSL（领域特定语言）定义的交易策略。具体的DSL定义存储在组合的parameters字段中的trade_strategy_dsl部分。此策略本身不包含具体的交易逻辑，而是作为DSL策略的标识符使用。',
    'DSL',
    NULL, -- DSL 本身不存储在此，而是存储在 portfolios.parameters 中
    '{}',  -- 此策略本身没有默认参数，具体参数在DSL定义中
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 添加注释以进一步说明
COMMENT ON TABLE strategy.strategies IS 'Stores definitions of trading and capital management strategies, supporting both code-based and DSL-based implementations.';
