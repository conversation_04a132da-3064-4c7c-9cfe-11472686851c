import { uuid } from '@cfworker/uuid';
import {
  PortfolioConfig,
  mqMessage,
  DBContext,
  PortfolioConfigCodeStrategyMQ,
  PortfolioConfigDslStrategyMQ,
  PortfolioDataFromDB
} from '../types';
import { getPortfolioConfig, getPortfolioDataFromDbForProcessing } from './helpers';

export async function constructPortfolioUpdateMessage(c: DBContext, code: string): Promise<mqMessage> {
  // 获取包含策略实现类型的组合数据
  const portfolioData = await getPortfolioDataFromDbForProcessing(c, code);

  // 根据策略类型构造不同格式的消息
  let portfolioConfig: PortfolioConfigCodeStrategyMQ | PortfolioConfigDslStrategyMQ;

  // 判断是否为DSL策略：优先检查实际的DSL数据，其次检查策略类型
  const isDslStrategy = portfolioData.parameters_from_db.trade_strategy_dsl || portfolioData.strategy_implementation_type === 'DSL';

  if (isDslStrategy) {
    // DSL策略组合 - 验证数据完整性
    if (!portfolioData.parameters_from_db.trade_strategy_dsl) {
      console.error(`Data integrity issue: Portfolio ${portfolioData.code} is marked as DSL strategy but missing trade_strategy_dsl data`, {
        portfolioCode: portfolioData.code,
        strategyImplementationType: portfolioData.strategy_implementation_type,
        parametersKeys: Object.keys(portfolioData.parameters_from_db)
      });
      throw new Error(`Portfolio ${portfolioData.code} data integrity issue: DSL strategy data is missing`);
    }

    // 构建strategy_definition，包含market_indicators（如果存在）
    const strategyDefinition: any = {
      trade_strategy: portfolioData.parameters_from_db.trade_strategy_dsl,
      capital_strategy: {
        name: portfolioData.capital_strategy_name,
        params: portfolioData.parameters_from_db.capital_strategy_params || {}
      }
    };

    // 添加market_indicators（如果存在）
    if (portfolioData.parameters_from_db.market_indicators) {
      strategyDefinition.market_indicators = portfolioData.parameters_from_db.market_indicators;
    }

    portfolioConfig = {
      name: portfolioData.name,
      code: portfolioData.code,
      description: portfolioData.description,
      strategy_definition: strategyDefinition,
      symbols: portfolioData.symbols_json,
      start_date: portfolioData.start_date,
      currency: portfolioData.currency,
      market: portfolioData.market,
      commission: portfolioData.commission,
      update_time: portfolioData.update_time,
      is_official: portfolioData.is_official,
      created_at: portfolioData.created_at,
      updated_at: portfolioData.updated_at,
      last_data_update_at: portfolioData.last_data_update_at,
      update_status: portfolioData.update_status
    } as PortfolioConfigDslStrategyMQ;
  } else {
    // 代码策略组合
    portfolioConfig = {
      name: portfolioData.name,
      code: portfolioData.code,
      description: portfolioData.description,
      strategy: {
        name: portfolioData.strategy_name,
        params: portfolioData.parameters_from_db.strategy_params || {}
      },
      capital_strategy: {
        name: portfolioData.capital_strategy_name,
        params: portfolioData.parameters_from_db.capital_strategy_params || {}
      },
      symbols: portfolioData.symbols_json,
      start_date: portfolioData.start_date,
      currency: portfolioData.currency,
      market: portfolioData.market,
      commission: portfolioData.commission,
      update_time: portfolioData.update_time,
      is_official: portfolioData.is_official,
      created_at: portfolioData.created_at,
      updated_at: portfolioData.updated_at,
      last_data_update_at: portfolioData.last_data_update_at,
      update_status: portfolioData.update_status
    } as PortfolioConfigCodeStrategyMQ;
  }

  return {
    job_id: uuid(),
    timestamp: new Date().toISOString(),
    action: 'create_or_update',
    portfolio_config: portfolioConfig
  };
}

export function constructPortfolioTimeFlyMessage(portfolioConfig: PortfolioConfig, year: string, newCode: string): mqMessage {
  const portfolioStartYear = parseInt(portfolioConfig.start_date.split('-')[0]);
  const requestedYear = parseInt(year);

  if (requestedYear < portfolioStartYear) {
    throw new Error(`Start date '${year}' is earlier than the portfolio start date '${portfolioConfig.start_date}'.`);
  }

  const newStartDate = `${year}-01-01`;

  return {
    job_id: uuid(),
    timestamp: new Date().toISOString(),
    action: 'create_or_update',
    portfolio_config: {
      ...portfolioConfig,
      code: newCode,
      start_date: newStartDate
    }
  };
}
