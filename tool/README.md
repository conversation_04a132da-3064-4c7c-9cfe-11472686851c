# Tools

This directory contains various development and maintenance tools for the My Invest Strategy Portfolio project.

## Sync Symbols (Python)

Synchronizes symbol data with the database.

- Install dependencies: `pip install -r requirements.txt`
- Create the environment file: `cp .env.example .env` and fill in the values
- Run the script: `python sync_symbols.py`

## Swagger UI Server (Python)

Serves a local Swagger UI interface for testing the API.

### Prerequisites

Install Python dependencies:

```bash
# Install dependencies
pip install -r requirements.txt

# Or install specific packages for Swagger UI
pip install flask pyyaml
```

### Usage

```bash
# From project root directory
python tool/serve_swagger.py

# Or from tool directory
cd tool && python serve_swagger.py
```

### Features

- 📖 Interactive API documentation
- 🧪 Direct API testing against configured servers
- 🔐 Built-in authentication support (x-auth-user header)
- 📊 Request/response logging
- 🔄 Auto-reload when OpenAPI spec changes

### Configuration

The server loads the OpenAPI specification from `doc/api/openapi.yaml`. You can modify the server URLs in the spec file to point to different environments:

- Production: `https://api.myinvestpilot.com/strategy_portfolio`
- Local Development: `http://localhost:8787/strategy_portfolio`

### Testing Workflow

1. Start the Swagger UI server: `node tool/serve-swagger.js`
2. Open <http://localhost:8888> in your browser
3. Click "Authorize" and enter your email address
4. Test API endpoints with the provided examples
5. Verify both Code Strategy and DSL Strategy formats

## AI Testing Tool (Python)

Simple script to test AI strategy generation with different prompt versions.

```bash
# Test all prompt versions with default cases
python test_ai.py

# Test with authentication
python test_ai.py --auth <EMAIL>

# Test specific versions only
python test_ai.py --versions v1.1,v2.0-experimental

# Custom API URL and timeout
python test_ai.py --url https://api.myinvestpilot.com/strategy_portfolio --timeout 60

# Show help
python test_ai.py --help
```

### Features

- Tests 6 different strategy generation scenarios
- Compares responses across multiple prompt versions (v1.0, v1.1, v2.0-experimental)
- Generates HTML report in `tool/test-results/` directory (gitignored)
- Automatically opens report in browser after completion
- Simple command-line interface with flexible options

### Test Cases

1. **基础双均线策略（中文）** - Chinese natural language
2. **Basic Dual MA Strategy (English)** - English natural language
3. **RSI策略** - Oscillator indicator strategy
4. **复杂多指标策略** - Complex multi-indicator combination
5. **JSON补全** - Incomplete JSON completion
6. **混合内容** - JSON + natural language mixed content

### Output

- **Console**: Real-time progress and summary statistics
- **HTML Report**: Detailed visual report with:
  - Test statistics and overview
  - Side-by-side version comparisons
  - Expandable JSON response content
  - Execution time analysis

Perfect for manual prompt optimization and A/B testing different system prompt versions.
