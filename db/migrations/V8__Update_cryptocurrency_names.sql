UPDATE symbols
SET name = 
    CASE symbol
        WHEN 'BTC' THEN 'Bitcoin'
        WHEN 'ETH' THEN 'Ethereum'
        WHEN 'XRP' THEN 'Ripple'
        WHEN 'LTC' THEN 'Litecoin'
        WHEN 'BCH' THEN 'Bitcoin Cash'
        WHEN 'ADA' THEN 'Cardano'
        WHEN 'DOT' THEN 'Polkadot'
        WHEN 'BNB' THEN 'Binance Coin'
        WHEN 'LINK' THEN 'Chainlink'
        WHEN 'XLM' THEN 'Stellar'
        WHEN 'SOL' THEN 'Solana'
        WHEN 'DOGE' THEN 'Dogecoin'
        WHEN 'TRX' THEN 'TRON'
        WHEN 'AVAX' THEN 'Avalanche'
        WHEN 'UNI' THEN 'Uniswap'
        ELSE name
    END
WHERE type = 'CRYPTO' AND symbol IN ('BTC', 'ETH', 'XRP', 'LTC', 'BCH', 'ADA', 'DOT', 'BNB', 'LINK', 'XLM', 'SOL', 'DOGE', 'TRX', 'AVAX', '<PERSON><PERSON>');